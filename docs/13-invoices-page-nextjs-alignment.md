# Invoices Page Next.js Alignment

## 📄 Invoices Page Restructure

### **Problem Identified**
Laravel invoices page had a different structure compared to Next.js frontend:
- Different header layout
- Missing metrics cards (YRR, MRR, Customers, Recurring Invoices)
- Table-based layout instead of customer breakdown
- Different visual design

### **Solution Implemented**

#### **1. Header Structure Update**
**File**: `laravel-app/resources/views/invoices/index.blade.php`

**New Header Layout:**
```html
<!-- Header -->
<div class="row align-items-center justify-content-between mb-4">
    <div class="col-auto">
        <div class="mb-2">
            <h6 class="text-muted mb-1 fw-bold" style="font-size: 20px;">Recurring</h6>
            <h1 class="fw-bold mb-0" style="font-size: 40px; line-height: 1;">Invoices</h1>
        </div>
    </div>
    <div class="col-auto">
        <a href="{{ route('invoices.create-advanced') }}" class="btn btn-primary">
            <i class="fas fa-plus me-2"></i>Create New
        </a>
    </div>
</div>
```

#### **2. Metrics Cards Implementation**
**Added 4 Dark Metrics Cards:**
```html
<!-- Metrics Cards -->
<div class="row g-3 mb-4">
    @php
        $totalYRR = $invoices->where('is_recurring', true)->where('status', 'active')->sum('total') * 12;
        $totalMRR = $invoices->where('is_recurring', true)->where('status', 'active')->sum('total');
        $uniqueCustomers = $invoices->pluck('contact_info.name')->unique()->count();
        $recurringCount = $invoices->where('is_recurring', true)->count();
    @endphp
    
    <div class="col-md-3">
        <div class="card bg-dark text-white h-100">
            <div class="card-body p-3 text-center">
                <h6 class="text-white-50 small mb-1">Total YRR</h6>
                <h4 class="fw-bold mb-0 text-white">CHF {{ number_format($totalYRR, 0, '.', ',') }}</h4>
            </div>
        </div>
    </div>
    
    <div class="col-md-3">
        <div class="card bg-dark text-white h-100">
            <div class="card-body p-3 text-center">
                <h6 class="text-white-50 small mb-1">Total MRR</h6>
                <h4 class="fw-bold mb-0 text-white">CHF {{ number_format($totalMRR, 0, '.', ',') }}</h4>
            </div>
        </div>
    </div>
    
    <div class="col-md-3">
        <div class="card bg-dark text-white h-100">
            <div class="card-body p-3 text-center">
                <h6 class="text-white-50 small mb-1">Customers</h6>
                <h4 class="fw-bold mb-0 text-white">{{ $uniqueCustomers }}</h4>
            </div>
        </div>
    </div>
    
    <div class="col-md-3">
        <div class="card bg-dark text-white h-100">
            <div class="card-body p-3 text-center">
                <h6 class="text-white-50 small mb-1">Recurring Invoices</h6>
                <h4 class="fw-bold mb-0 text-white">{{ $recurringCount }}</h4>
            </div>
        </div>
    </div>
</div>
```

#### **3. Customer Breakdown Layout**
**Replaced Table with Customer-Grouped Cards:**
```html
<!-- Customer Breakdown -->
<div class="customer-breakdown">
    @foreach($customerGroups as $customerName => $customerInvoices)
        @php
            $customerYRR = $customerInvoices->where('is_recurring', true)->where('status', 'active')->sum('total') * 12;
            $firstInvoice = $customerInvoices->first();
        @endphp
        
        <div class="card mb-3 border-0 shadow-sm">
            <!-- Customer Header -->
            <div class="card-header bg-white border-bottom-0 py-3">
                <div class="row align-items-center">
                    <div class="col">
                        <div class="d-flex align-items-center">
                            <div class="bg-primary text-white rounded-circle d-flex align-items-center justify-content-center me-3" style="width: 45px; height: 45px;">
                                <span class="fw-bold">{{ substr($customerName ?? 'U', 0, 1) }}</span>
                            </div>
                            <div>
                                <h6 class="mb-1 fw-semibold">{{ $customerName ?? 'Unknown Customer' }}</h6>
                                <small class="text-muted">{{ $firstInvoice->contact_info['email'] ?? '' }}</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-auto">
                        <div class="d-flex align-items-center">
                            <span class="fw-semibold me-3">YRR: CHF {{ number_format($customerYRR, 0, '.', ',') }}</span>
                            <button class="btn btn-sm btn-outline-secondary" type="button" data-bs-toggle="collapse" data-bs-target="#customer-{{ $loop->index }}">
                                <i class="fas fa-chevron-down"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Customer Invoices -->
            <div class="collapse show" id="customer-{{ $loop->index }}">
                <div class="card-body p-0">
                    @foreach($customerInvoices as $invoice)
                        <div class="d-flex align-items-center p-3 {{ !$loop->last ? 'border-bottom' : '' }}">
                            <div class="me-3">
                                @if($invoice->is_recurring)
                                    <div class="bg-info text-white rounded d-flex align-items-center justify-content-center" style="width: 35px; height: 35px;">
                                        <i class="fas fa-sync-alt"></i>
                                    </div>
                                @else
                                    <div class="bg-secondary text-white rounded d-flex align-items-center justify-content-center" style="width: 35px; height: 35px;">
                                        <i class="fas fa-file-invoice"></i>
                                    </div>
                                @endif
                            </div>
                            <div class="flex-grow-1">
                                <div class="d-flex justify-content-between align-items-start">
                                    <div>
                                        <h6 class="mb-1">{{ $invoice->title ?? $invoice->document_nr }}</h6>
                                        <small class="text-muted">{{ $invoice->document_nr }} • {{ $invoice->created_at->format('M j, Y') }}</small>
                                    </div>
                                    <div class="text-end">
                                        <div class="fw-semibold">CHF {{ number_format($invoice->total, 2) }}</div>
                                        <span class="badge bg-{{ $statusColors[$invoice->status] ?? 'secondary' }} bg-opacity-10">
                                            {{ ucfirst($invoice->status) }}
                                        </span>
                                    </div>
                                </div>
                                <div class="mt-2">
                                    @if($invoice->is_recurring && $invoice->recurringTemplate)
                                        <span class="badge bg-info bg-opacity-10 text-info me-2">
                                            <i class="fas fa-sync-alt me-1"></i>{{ ucfirst($invoice->recurringTemplate->interval) }}
                                        </span>
                                        @if($invoice->recurringTemplate->next_run)
                                            <small class="text-muted">Next: {{ \Carbon\Carbon::parse($invoice->recurringTemplate->next_run)->format('M j, Y') }}</small>
                                        @endif
                                    @endif
                                </div>
                            </div>
                            <div class="ms-3">
                                <div class="dropdown">
                                    <button class="btn btn-sm btn-outline-secondary" type="button" data-bs-toggle="dropdown">
                                        <i class="fas fa-ellipsis-v"></i>
                                    </button>
                                    <ul class="dropdown-menu dropdown-menu-end">
                                        <li><a class="dropdown-item" href="{{ route('invoices.show', $invoice) }}">View</a></li>
                                        <li><a class="dropdown-item" href="{{ route('invoices.edit', $invoice) }}">Edit</a></li>
                                        <li><hr class="dropdown-divider"></li>
                                        <li>
                                            <form method="POST" action="{{ route('invoices.destroy', $invoice) }}">
                                                @csrf @method('DELETE')
                                                <button type="submit" class="dropdown-item text-danger">Delete</button>
                                            </form>
                                        </li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    @endforeach
                </div>
            </div>
        </div>
    @endforeach
</div>
```

#### **4. Controller Updates**
**File**: `laravel-app/app/Http/Controllers/InvoiceController.php`

**Updated Index Method:**
```php
public function index()
{
    $user = Auth::user();
    
    // Get all invoices for the user with recurring templates
    $invoices = Invoice::where('user_id', $user->id)
        ->with('recurringTemplate')
        ->orderBy('created_at', 'desc')
        ->get(); // Use get() instead of paginate() for metrics calculation

    return view('invoices.index', compact('invoices'));
}
```

### **5. Key Features Implemented**

#### **Visual Design:**
- ✅ **Header**: "Recurring" (small) + "Invoices" (large) typography
- ✅ **Dark metrics cards** with white text
- ✅ **Customer avatars** with initials
- ✅ **Collapsible customer sections**
- ✅ **Clean card-based layout**

#### **Metrics Calculation:**
- ✅ **Total YRR**: Annual recurring revenue (MRR × 12)
- ✅ **Total MRR**: Monthly recurring revenue from active recurring invoices
- ✅ **Customers**: Unique customer count
- ✅ **Recurring Invoices**: Total count of recurring invoices

#### **Customer Breakdown:**
- ✅ **Grouped by customer** with YRR per customer
- ✅ **Expandable/collapsible** invoice lists
- ✅ **Invoice type icons** (recurring vs one-time)
- ✅ **Status badges** and amount display
- ✅ **Action dropdown** for each invoice

#### **Functionality:**
- ✅ **Next run dates** for recurring invoices
- ✅ **Interval display** (monthly, weekly, etc.)
- ✅ **View/Edit/Delete** actions
- ✅ **Responsive design**

### **6. Next.js Alignment Status**

| **Aspect** | **Next.js Frontend** | **Laravel Backend** | **Status** |
|------------|---------------------|-------------------|------------|
| Header Layout | "Recurring" + "Invoices" | "Recurring" + "Invoices" | ✅ Aligned |
| Metrics Cards | YRR, MRR, Customers, Recurring | YRR, MRR, Customers, Recurring | ✅ Aligned |
| Card Design | Dark background, white text | Dark background, white text | ✅ Aligned |
| Customer Grouping | Grouped by customer | Grouped by customer | ✅ Aligned |
| YRR per Customer | Displayed per customer | Displayed per customer | ✅ Aligned |
| Collapsible Sections | Expandable customer sections | Expandable customer sections | ✅ Aligned |
| Create Button | "Create New" button | "Create New" button | ✅ Aligned |

---

**Status**: ✅ **COMPLETED** - Invoices page now fully matches Next.js frontend structure with proper metrics, customer breakdown, and visual design alignment.
