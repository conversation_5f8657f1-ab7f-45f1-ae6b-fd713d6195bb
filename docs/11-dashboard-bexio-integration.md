# Dashboard Bexio Integration - <PERSON> Recurring Focus

## Overview

Dashboard telah diupdate untuk menggunakan `BexioRealService` dengan fokus pada fitur utama Kim: **recurring invoices**. Dashboard tidak menampilkan semua invoice dari Bexio, melainkan hanya invoice yang dibuat di aplikasi Kim (terutama recurring invoices).

## Key Changes

### 1. **Dashboard Controller Integration**
- Dashboard route sekarang menggunakan `DashboardController` instead of inline closure
- Menggunakan dependency injection untuk `BexioRealService` dan `BexioSyncService`
- Fokus pada invoice Kim yang tersimpan di database lokal

### 2. **Kim-Focused Data Strategy**
```php
// Dashboard hanya menampilkan invoice Kim (bukan semua invoice Bexio)
$kimInvoices = Invoice::forOrganization($organization->id)->get();
$recurringInvoices = $kimInvoices->where('is_recurring', true);
```

### 3. **Status Sync Only**
Dashboard melakukan sync status invoice Kim yang sudah dikirim ke Bexio:
```php
private function syncKimRecurringInvoicesStatus($organization, string $accessToken)
{
    // Hanya sync status invoice Kim yang ada di Bexio (punya bexio_id)
    $kimInvoicesInBexio = Invoice::forOrganization($organization->id)
        ->fromBexio()
        ->get();
}
```

## Database Schema Updates

### New Columns Added to `invoices` table:
```sql
-- Migration: 2025_07_06_152532_add_sync_columns_to_invoices_table
ALTER TABLE invoices ADD COLUMN title VARCHAR(255) NULL AFTER bexio_id;
ALTER TABLE invoices MODIFY COLUMN bexio_id VARCHAR(255) NULL;
ALTER TABLE invoices ADD INDEX idx_org_bexio (organization_id, bexio_id);
ALTER TABLE invoices ADD COLUMN last_synced_at TIMESTAMP NULL AFTER updated_at;
```

### Invoice Model Updates:
```php
protected $fillable = [
    'user_id', 'organization_id', 'bexio_id', 'title', 'document_nr',
    'contact_info', 'total', 'status', 'is_recurring', 
    'recurring_settings', 'last_synced_at'
];

// New helper methods
public function isFromBexio() // Has bexio_id
public function needsSync()   // Needs status update from Bexio
public function markAsSynced() // Update last_synced_at
```

## Services Architecture

### 1. **BexioRealService Enhancements**
```php
// Implemented missing methods
public function getRecurringInvoices(): array
public function getDrafts(): array
public function getInvoicesByStatus(int $statusId): array
public function getUnpaidInvoices(): array
public function getPaidInvoices(): array
```

### 2. **BexioSyncService** (New)
Dedicated service untuk sinkronisasi data:
```php
class BexioSyncService
{
    public function syncInvoicesForOrganization(Organization $org, string $token): array
    public function syncRecurringInvoicesForOrganization(Organization $org, string $token): array
    public function getSyncStats(Organization $org): array
}
```

## Dashboard Features

### 1. **Kim-Focused Metrics**
- **Kim Invoices**: Total invoice yang dibuat di Kim
- **Total Revenue**: Revenue dari invoice Kim yang paid
- **Unpaid**: Invoice Kim yang belum paid
- **Recurring**: Fitur utama Kim - recurring invoices

### 2. **Data Source Indicators**
Dashboard menampilkan status data:
- `kim_recurring`: Data Kim dengan status sync dari Bexio
- `local_database`: Fallback ke data lokal jika Bexio tidak tersedia

### 3. **Status Sync Logic**
```php
// Mapping Bexio status ke Kim status
1 => 'draft'     // Draft
2 => 'sent'      // Pending/Sent  
3 => 'sent'      // Sent
4 => 'paid'      // Paid
5 => 'cancelled' // Cancelled
6 => 'sent'      // Overdue (treat as sent)
```

## Key Benefits

### 1. **Performance**
- Tidak mengimpor semua invoice Bexio
- Hanya sync status invoice Kim yang relevan
- Database lokal untuk performa cepat

### 2. **Focus on Core Feature**
- Dashboard fokus pada recurring invoices (fitur utama Kim)
- Invoice non-recurring dihandle langsung di Bexio
- Clear separation of concerns

### 3. **Reliable Fallback**
- Jika Bexio API tidak tersedia, dashboard tetap berfungsi dengan data lokal
- Status sync berjalan di background tanpa mengganggu UX

## Usage Flow

1. **User Login** → Dashboard loads
2. **Kim Data Load** → Get invoices from local database
3. **Status Sync** → Update status of Kim invoices that exist in Bexio
4. **Display** → Show Kim's recurring invoices with updated status

## Configuration

Dashboard menggunakan existing Bexio configuration:
```env
BEXIO_MODE=real
BEXIO_CLIENT_ID=your_client_id
BEXIO_CLIENT_SECRET=your_client_secret
BEXIO_API_BASE_URL=https://api.bexio.com
```

## Error Handling

- **Bexio API Unavailable**: Fallback to local data
- **Token Expired**: Handled by `fresh-token` middleware
- **Sync Errors**: Logged but don't break dashboard functionality
- **Individual Invoice Sync Fails**: Continue with other invoices

## Next Steps

1. **Invoice Creation**: Implement creating recurring invoices in Kim
2. **Bexio Push**: Send Kim invoices to Bexio API
3. **Recurring Logic**: Implement automatic recurring invoice generation
4. **Status Notifications**: Alert users when invoice status changes in Bexio
