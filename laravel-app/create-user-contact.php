<?php

require_once __DIR__ . '/vendor/autoload.php';

// Bootstrap Laravel
$app = require_once __DIR__ . '/bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

echo "👤 Creating Contact with User Email\n";
echo "===================================\n\n";

// Get authenticated user
$user = \App\Models\User::first();
if (!$user) {
    echo "❌ Error: No user found in database\n";
    exit(1);
}

// Authenticate user for API calls
\Illuminate\Support\Facades\Auth::login($user);

echo "✅ User authenticated: {$user->name}\n";
echo "📧 User email: {$user->email}\n";
echo "🎯 Creating contact with user's email to bypass trial limitation\n\n";

try {
    // Create controller instance
    $controller = new \App\Http\Controllers\Api\BexioApiController();
    
    // Prepare contact data with user's email
    $request = new \Illuminate\Http\Request();
    $request->merge([
        'customer' => [
            'name' => $user->name,
            'email' => $user->email, // Use user's email
            'type' => 2, // Individual person
            'address' => 'User Address',
            'city' => 'User City',
            'postcode' => '12345',
            'phone' => '****** 567 8900',
            'country_id' => 1
        ]
    ]);
    
    echo "📡 Creating contact in Bexio...\n";
    echo "   - Name: {$user->name}\n";
    echo "   - Email: {$user->email}\n\n";
    
    // Create contact via API
    $response = $controller->createCustomer($request);
    $result = json_decode($response->getContent(), true);
    
    if (isset($result['success']) && $result['success']) {
        $contactData = $result['data'];
        echo "✅ Contact created successfully!\n";
        echo "   - Contact ID: {$contactData['id']}\n";
        echo "   - Name: {$contactData['name_1']}\n";
        echo "   - Email: {$contactData['mail']}\n\n";
        
        // Now create invoice with this contact
        echo "📄 Creating invoice with user contact...\n";
        
        $invoice = \App\Models\Invoice::create([
            'user_id' => $user->id,
            'title' => 'User Email Test Invoice',
            'document_nr' => 'INV-USER-TEST-' . date('Ymd-His'),
            'contact_info' => json_encode([
                'contact_id' => $contactData['id'], // Use the new contact ID
                'name' => $user->name,
                'email' => $user->email
            ]),
            'total' => 1200.00,
            'status' => 'active',
            'is_recurring' => false,
            'items' => json_encode([
                [
                    'name' => 'User Email Test Service',
                    'quantity' => 8,
                    'unit_price' => 150,
                    'unit_id' => 2, // Hours
                    'tax_id' => 3   // UEX - Export/Exempt 0.00%
                ]
            ]),
            'tax_status' => 0,
            'organization_id' => $user->organization_id ?? 1
        ]);
        
        echo "✅ Invoice created successfully!\n";
        echo "   - Invoice ID: {$invoice->id}\n";
        echo "   - Document Nr: {$invoice->document_nr}\n";
        echo "   - Total: CHF " . number_format($invoice->total, 2) . "\n";
        
        $contactInfo = json_decode($invoice->contact_info, true);
        echo "   - Contact ID: {$contactInfo['contact_id']}\n";
        echo "   - Contact Email: {$contactInfo['email']}\n\n";
        
        // Step 1: Create invoice in Bexio
        echo "🔄 Step 1: Creating invoice in Bexio...\n";
        
        $invoiceRequest = new \Illuminate\Http\Request();
        $invoiceRequest->merge(['invoice_id' => $invoice->id]);
        
        $invoiceResponse = $controller->createInvoice($invoiceRequest);
        $invoiceResult = json_decode($invoiceResponse->getContent(), true);
        
        if (isset($invoiceResult['success']) && $invoiceResult['success']) {
            echo "✅ Invoice created in Bexio!\n";
            echo "   - Bexio Invoice ID: {$invoiceResult['bexio_id']}\n\n";
            
            // Refresh invoice data
            $invoice->refresh();
            
            // Step 2: Send email to user's own email
            echo "📧 Step 2: Sending invoice email to user's email...\n";
            echo "   - This should work because trial allows sending to user's own email\n";
            
            $emailRequest = new \Illuminate\Http\Request();
            $emailRequest->merge([
                'invoice_id' => $invoice->id,
                'recipient_email' => $user->email // Send to user's own email
            ]);
            
            $emailResponse = $controller->sendInvoiceEmail($emailRequest);
            $emailResult = json_decode($emailResponse->getContent(), true);
            
            if (isset($emailResult['success']) && $emailResult['success']) {
                echo "🎉 COMPLETE SUCCESS!\n";
                echo "✅ Contact created in Bexio\n";
                echo "✅ Invoice created in Bexio (ID: {$invoice->bexio_id})\n";
                echo "✅ Email sent successfully to {$user->email}\n\n";
                
                echo "📧 CHECK YOUR EMAIL INBOX!\n";
                echo "   - Email: {$user->email}\n";
                echo "   - Subject: Invoice {$invoice->document_nr}\n";
                echo "   - The invoice should be attached as PDF\n\n";
                
            } else {
                echo "❌ Failed to send email\n";
                echo "   - Error: " . ($emailResult['error'] ?? 'Unknown error') . "\n\n";
                
                // Show what was sent
                echo "🔍 Debug - Email data sent:\n";
                echo "   - Recipient: {$user->email}\n";
                echo "   - Invoice Bexio ID: {$invoice->bexio_id}\n";
                echo "   - Subject: Invoice {$invoice->document_nr} - CHF " . number_format($invoice->total, 2) . "\n\n";
            }
            
        } else {
            echo "❌ Failed to create invoice in Bexio\n";
            echo "   - Error: " . ($invoiceResult['error'] ?? 'Unknown error') . "\n\n";
        }
        
    } else {
        echo "❌ Failed to create contact\n";
        echo "   - Error: " . ($result['error'] ?? $result['message'] ?? 'Unknown error') . "\n\n";
    }
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n\n";
}

echo "📊 Final Summary:\n";
echo "================\n";
echo "This test creates a contact with the user's own email address to bypass\n";
echo "Bexio trial limitations that only allow sending emails to the account owner.\n\n";

echo "Expected Results:\n";
echo "1. ✅ Contact created with user email: {$user->email}\n";
echo "2. ✅ Invoice created in Kim Rebill database\n";
echo "3. ✅ Invoice created in Bexio via API\n";
echo "4. ✅ Email sent to user's email (should work with trial)\n\n";

echo "💡 If successful, check {$user->email} for the invoice email!\n";
