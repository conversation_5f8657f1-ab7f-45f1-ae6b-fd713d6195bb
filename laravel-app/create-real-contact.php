<?php

require_once __DIR__ . '/vendor/autoload.php';

use Illuminate\Foundation\Application;

// Bootstrap Laravel
$app = require_once __DIR__ . '/bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

echo "🧑‍💼 Creating Real Contact for Email Testing\n";
echo "==========================================\n\n";

// Check configuration
if (!config('bexio.personal_access_token')) {
    echo "❌ Error: BEXIO_PERSONAL_ACCESS_TOKEN not configured\n";
    exit(1);
}

echo "✅ Bexio Personal Access Token configured\n";
echo "📧 Creating contact for: <EMAIL>\n\n";

// Get authenticated user
$user = \App\Models\User::first();
if (!$user) {
    echo "❌ Error: No user found in database\n";
    exit(1);
}

// Authenticate user for API calls
\Illuminate\Support\Facades\Auth::login($user);

try {
    // Create controller instance
    $controller = new \App\Http\Controllers\Api\BexioApiController();

    // Prepare contact data in correct format for createCustomer API
    $request = new \Illuminate\Http\Request();
    $request->merge([
        'customer' => [
            'name' => 'Doni Bageur',
            'email' => '<EMAIL>',
            'type' => 2, // Individual person
            'address' => 'Jakarta, Indonesia',
            'city' => 'Jakarta',
            'postcode' => '12345',
            'phone' => '+62 21 123 4567',
            'country_id' => 1
        ]
    ]);

    echo "📡 Calling Bexio API to create contact...\n";

    // Create contact via API
    $response = $controller->createCustomer($request);
    $result = json_decode($response->getContent(), true);

    if (isset($result['success']) && $result['success']) {
        $contactData = $result['data'];
        echo "✅ Contact created successfully!\n";
        echo "   - Contact ID: {$contactData['id']}\n";
        echo "   - Name: {$contactData['name_1']}\n";
        echo "   - Email: {$contactData['mail']}\n";
        echo "   - Type: " . ($contactData['contact_type_id'] == 2 ? 'Individual' : 'Company') . "\n\n";

        // Now create invoice with this contact
        echo "📄 Creating test invoice with new contact...\n";

        $invoice = \App\Models\Invoice::create([
            'user_id' => $user->id,
            'title' => 'Real Email Test Invoice',
            'document_nr' => 'INV-EMAIL-TEST-' . date('Ymd-His'),
            'contact_id' => (int) $contactData['id'], // Use the new contact ID (ensure integer)
            'contact_info' => json_encode([
                'name' => 'Doni Bageur',
                'email' => '<EMAIL>'
            ]),
            'total' => 750.00,
            'status' => 'active',
            'is_recurring' => false,
            'items' => json_encode([
                [
                    'name' => 'Email Testing Service',
                    'quantity' => 5,
                    'unit_price' => 150,
                    'unit_id' => 2, // Hours (valid from Bexio)
                    'tax_id' => 3   // UEX - Export/Exempt 0.00% (valid from Bexio)
                ]
            ]),
            'tax_status' => 0,
            'organization_id' => $user->organization_id ?? 1
        ]);

        // Debug: Show the contact_id that was saved
        echo "   - Saved Contact ID: {$invoice->contact_id}\n";

        echo "✅ Invoice created successfully!\n";
        echo "   - Invoice ID: {$invoice->id}\n";
        echo "   - Document Nr: {$invoice->document_nr}\n";
        echo "   - Total: CHF " . number_format($invoice->total, 2) . "\n";
        echo "   - Contact ID: {$invoice->contact_id}\n\n";

        // Test invoice creation in Bexio
        echo "🔄 Step 1: Creating invoice in Bexio...\n";

        $invoiceRequest = new \Illuminate\Http\Request();
        $invoiceRequest->merge(['invoice_id' => $invoice->id]);

        $invoiceResponse = $controller->createInvoice($invoiceRequest);
        $invoiceResult = json_decode($invoiceResponse->getContent(), true);

        if (isset($invoiceResult['success']) && $invoiceResult['success']) {
            echo "✅ Invoice created in Bexio!\n";
            echo "   - Bexio Invoice ID: {$invoiceResult['bexio_id']}\n\n";

            // Refresh invoice data
            $invoice->refresh();

            // Test email sending
            echo "📧 Step 2: Sending invoice email...\n";

            $emailRequest = new \Illuminate\Http\Request();
            $emailRequest->merge([
                'invoice_id' => $invoice->id,
                'recipient_email' => '<EMAIL>'
            ]);

            $emailResponse = $controller->sendInvoiceEmail($emailRequest);
            $emailResult = json_decode($emailResponse->getContent(), true);

            if (isset($emailResult['success']) && $emailResult['success']) {
                echo "✅ Invoice email sent successfully!\n";
                echo "   - Recipient: <EMAIL>\n";
                echo "   - Check your email inbox for the invoice!\n\n";

                // Show template preview
                echo "📄 Email Template Preview:\n";
                echo str_repeat('-', 50) . "\n";

                $template = \App\Helpers\EmailTemplateHelper::getUserTemplate($user);

                $templateData = [
                    'total' => number_format($invoice->total, 2),
                    'date' => now()->format('M j, Y'),
                    'valid_until' => now()->addDays(30)->format('M j, Y'),
                    'document_number' => $invoice->document_nr,
                    'title' => $invoice->title,
                    'currency' => 'CHF',
                    'name' => 'Doni Bageur',
                    'name_1' => 'Doni Bageur',
                    'user' => $user->name,
                    'user_email' => $user->email,
                    'company_name' => $user->organization->name ?? 'Your Company',
                    'network_link' => "https://office.bexio.com/index.php/kb_invoice/show/id/{$invoice->bexio_id}"
                ];

                $subject = \App\Helpers\EmailTemplateHelper::replaceVariables($template['subject'], $templateData);
                $body = \App\Helpers\EmailTemplateHelper::replaceVariables($template['body'], $templateData);

                echo "Subject: {$subject}\n\n";
                echo $body . "\n";
                echo str_repeat('-', 50) . "\n\n";

            } else {
                echo "❌ Failed to send email\n";
                echo "   - Error: " . ($emailResult['error'] ?? 'Unknown error') . "\n\n";
            }

        } else {
            echo "❌ Failed to create invoice in Bexio\n";
            echo "   - Error: " . ($invoiceResult['error'] ?? 'Unknown error') . "\n\n";
        }

    } else {
        echo "❌ Failed to create contact\n";
        echo "   - Error: " . ($result['error'] ?? $result['message'] ?? 'Unknown error') . "\n\n";
    }

} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n\n";
}

echo "🎉 Process completed!\n";
echo "\n💡 Summary:\n";
echo "1. Contact created in Bexio with email: <EMAIL>\n";
echo "2. Invoice created in Kim Rebill database\n";
echo "3. Invoice created in Bexio via API\n";
echo "4. Email <NAME_EMAIL> with invoice template\n";
echo "\n📧 Please check your email inbox for the invoice!\n";
