<?php

/**
 * Bexio Contact API Test using Laravel Environment
 * 
 * This script uses <PERSON><PERSON>'s environment configuration
 * to get the Bexio access token and test the API
 */

require_once __DIR__ . '/vendor/autoload.php';

use Illuminate\Support\Facades\Http;
use Dotenv\Dotenv;

// Load Laravel environment
if (file_exists(__DIR__ . '/.env')) {
    $dotenv = Dotenv::createImmutable(__DIR__);
    $dotenv->load();
}

// Configuration
$accessToken = $_ENV['BEXIO_ACCESS_TOKEN'] ?? null;
$baseUrl = 'https://api.bexio.com/2.0';

/**
 * Test GET /contact using Laravel HTTP client
 */
function testGetContactsLaravel($accessToken, $baseUrl) {
    echo "\n=== Testing GET /contact (Laravel HTTP) ===\n";
    
    try {
        $response = Http::withHeaders([
            'Accept' => 'application/json',
            'Authorization' => 'Bearer ' . $accessToken,
        ])->timeout(30)->get($baseUrl . '/contact');
        
        if ($response->successful()) {
            $data = $response->json();
            echo "✓ SUCCESS - Status: {$response->status()}\n";
            echo "✓ Retrieved " . count($data) . " contacts\n";
            
            if (!empty($data)) {
                $first = $data[0];
                echo "✓ First contact: {$first['name_1']} (ID: {$first['id']})\n";
                echo "✓ Email: {$first['mail']}\n";
                echo "✓ Type: {$first['contact_type_id']}\n";
            }
            
            return $data;
        } else {
            echo "✗ FAILED - Status: {$response->status()}\n";
            echo "✗ Error: {$response->body()}\n";
            return false;
        }
    } catch (Exception $e) {
        echo "✗ EXCEPTION: {$e->getMessage()}\n";
        return false;
    }
}

/**
 * Test POST /contact using Laravel HTTP client
 */
function testCreateContactLaravel($accessToken, $baseUrl) {
    echo "\n=== Testing POST /contact (Laravel HTTP) ===\n";
    
    $contactData = [
        'contact_type_id' => 1,
        'name_1' => 'Kim Rebill Laravel Test ' . date('H:i:s'),
        'salutation_id' => 2,
        'address' => 'Laravel Street 123',
        'street_name' => 'Laravel Street',
        'house_number' => '123',
        'address_addition' => 'Suite 456',
        'postcode' => '8001',
        'city' => 'Zurich',
        'country_id' => 1,
        'mail' => 'laravel.test.' . time() . '@kimrebill.com',
        'mail_second' => '',
        'phone_fixed' => '+41 44 987 65 43',
        'phone_fixed_second' => '',
        'phone_mobile' => '+41 79 987 65 43',
        'fax' => '',
        'url' => 'https://kimrebill.com',
        'skype_name' => '',
        'remarks' => 'Created by Kim Rebill Laravel test script',
        'contact_group_ids' => '1,2',
        'user_id' => 1,
        'owner_id' => 1
    ];
    
    echo "Creating contact: {$contactData['name_1']}\n";
    echo "Email: {$contactData['mail']}\n";
    
    try {
        $response = Http::withHeaders([
            'Content-Type' => 'application/json',
            'Accept' => 'application/json',
            'Authorization' => 'Bearer ' . $accessToken,
        ])->timeout(30)->post($baseUrl . '/contact', $contactData);
        
        if ($response->successful()) {
            $data = $response->json();
            echo "✓ SUCCESS - Status: {$response->status()}\n";
            echo "✓ Created contact with ID: {$data['id']}\n";
            echo "✓ Name: {$data['name_1']}\n";
            echo "✓ Email: {$data['mail']}\n";
            echo "✓ Address: {$data['address']}, {$data['city']}\n";
            
            return $data;
        } else {
            echo "✗ FAILED - Status: {$response->status()}\n";
            echo "✗ Error: {$response->body()}\n";
            return false;
        }
    } catch (Exception $e) {
        echo "✗ EXCEPTION: {$e->getMessage()}\n";
        return false;
    }
}

/**
 * Test the BexioApiController endpoints
 */
function testBexioApiController() {
    echo "\n=== Testing BexioApiController Endpoints ===\n";
    
    $baseAppUrl = 'http://127.0.0.1:8000'; // Adjust if different
    
    try {
        // Test GET contacts endpoint
        echo "Testing GET /api/bexio/contacts...\n";
        $response = Http::timeout(30)->get($baseAppUrl . '/api/bexio/contacts');
        
        if ($response->successful()) {
            $data = $response->json();
            echo "✓ SUCCESS - Laravel API returned " . count($data['data'] ?? []) . " contacts\n";
        } else {
            echo "✗ FAILED - Laravel API error: {$response->body()}\n";
        }
        
        // Test GET units endpoint
        echo "Testing GET /api/bexio/units...\n";
        $response = Http::timeout(30)->get($baseAppUrl . '/api/bexio/units');
        
        if ($response->successful()) {
            $data = $response->json();
            echo "✓ SUCCESS - Laravel API returned " . count($data['data'] ?? []) . " units\n";
        } else {
            echo "✗ FAILED - Laravel API error: {$response->body()}\n";
        }
        
        // Test GET taxes endpoint
        echo "Testing GET /api/bexio/taxes...\n";
        $response = Http::timeout(30)->get($baseAppUrl . '/api/bexio/taxes');
        
        if ($response->successful()) {
            $data = $response->json();
            echo "✓ SUCCESS - Laravel API returned " . count($data['data'] ?? []) . " taxes\n";
        } else {
            echo "✗ FAILED - Laravel API error: {$response->body()}\n";
        }
        
    } catch (Exception $e) {
        echo "✗ EXCEPTION testing Laravel API: {$e->getMessage()}\n";
    }
}

// Main execution
echo "Bexio Contact API Test (Laravel Integration)\n";
echo "===========================================\n";

// Check environment
if (!$accessToken) {
    echo "✗ BEXIO_ACCESS_TOKEN not found in .env file\n";
    echo "Please add BEXIO_ACCESS_TOKEN=your_token_here to your .env file\n";
    exit(1);
}

echo "✓ Access token loaded from .env\n";
echo "✓ Base URL: {$baseUrl}\n";
echo "✓ Token preview: " . substr($accessToken, 0, 20) . "...\n";

// Run direct API tests
$contacts = testGetContactsLaravel($accessToken, $baseUrl);
$newContact = testCreateContactLaravel($accessToken, $baseUrl);

// Test Laravel API endpoints
testBexioApiController();

echo "\n=== Test Summary ===\n";
echo "Direct GET /contact: " . ($contacts !== false ? "✓ PASSED" : "✗ FAILED") . "\n";
echo "Direct POST /contact: " . ($newContact !== false ? "✓ PASSED" : "✗ FAILED") . "\n";

echo "\n=== Environment Check ===\n";
echo "BEXIO_ACCESS_TOKEN: " . ($accessToken ? "✓ SET" : "✗ NOT SET") . "\n";
echo "Laravel App URL: http://127.0.0.1:8000\n";

echo "\nDone!\n";
