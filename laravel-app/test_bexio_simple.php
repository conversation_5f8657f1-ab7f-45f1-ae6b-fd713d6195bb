<?php

/**
 * Simple Bexio Contact API Test
 * 
 * Quick test script for Bexio Contact API using cURL
 * Based on the provided curl examples
 */

// Configuration - UPDATE THIS WITH YOUR ACCESS TOKEN
$accessToken = 'YOUR_ACCESS_TOKEN_HERE'; // Replace with actual token
$baseUrl = 'https://api.bexio.com/2.0';

/**
 * Make cURL request
 */
function makeCurlRequest($url, $method = 'GET', $data = null, $headers = []) {
    $ch = curl_init();
    
    // Basic cURL options
    curl_setopt_array($ch, [
        CURLOPT_URL => $url,
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_TIMEOUT => 30,
        CURLOPT_HTTPHEADER => $headers,
        CURLOPT_SSL_VERIFYPEER => true,
    ]);
    
    // Set method and data
    switch (strtoupper($method)) {
        case 'POST':
            curl_setopt($ch, CURLOPT_POST, true);
            if ($data) {
                curl_setopt($ch, CURLOPT_POSTFIELDS, $data);
            }
            break;
        case 'PUT':
            curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'PUT');
            if ($data) {
                curl_setopt($ch, CURLOPT_POSTFIELDS, $data);
            }
            break;
        case 'DELETE':
            curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'DELETE');
            break;
    }
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    curl_close($ch);
    
    return [
        'success' => !$error && $httpCode >= 200 && $httpCode < 300,
        'http_code' => $httpCode,
        'response' => $response,
        'error' => $error
    ];
}

/**
 * Test GET /contact
 */
function testGetContacts($accessToken, $baseUrl) {
    echo "\n=== Testing GET /contact ===\n";
    
    $headers = [
        'Accept: application/json',
        'Authorization: Bearer ' . $accessToken
    ];
    
    $result = makeCurlRequest($baseUrl . '/contact', 'GET', null, $headers);
    
    if ($result['success']) {
        echo "✓ SUCCESS - HTTP {$result['http_code']}\n";
        $data = json_decode($result['response'], true);
        echo "✓ Retrieved " . count($data) . " contacts\n";
        
        // Show first contact
        if (!empty($data)) {
            $first = $data[0];
            echo "✓ First contact: {$first['name_1']} (ID: {$first['id']})\n";
        }
        
        return $data;
    } else {
        echo "✗ FAILED - HTTP {$result['http_code']}\n";
        echo "✗ Error: {$result['error']}\n";
        echo "✗ Response: {$result['response']}\n";
        return false;
    }
}

/**
 * Test POST /contact
 */
function testCreateContact($accessToken, $baseUrl) {
    echo "\n=== Testing POST /contact ===\n";
    
    $headers = [
        'Content-Type: application/json',
        'Accept: application/json',
        'Authorization: Bearer ' . $accessToken
    ];
    
    // Contact data from the curl example
    $contactData = [
        'contact_type_id' => 1,
        'name_1' => 'Kim Rebill Test Company',
        'salutation_id' => 2,
        'address' => 'Smith Street 22',
        'street_name' => 'Smith Street',
        'house_number' => '77',
        'address_addition' => 'Building C',
        'postcode' => '8004',
        'city' => 'Zurich',
        'country_id' => 1,
        'mail' => '<EMAIL>',
        'mail_second' => '',
        'phone_fixed' => '',
        'phone_fixed_second' => '',
        'phone_mobile' => '',
        'fax' => '',
        'url' => '',
        'skype_name' => '',
        'remarks' => 'Created by Kim Rebill test script',
        'contact_group_ids' => '1,2',
        'user_id' => 1,
        'owner_id' => 1
    ];
    
    echo "Creating contact: {$contactData['name_1']}\n";
    
    $result = makeCurlRequest(
        $baseUrl . '/contact', 
        'POST', 
        json_encode($contactData), 
        $headers
    );
    
    if ($result['success']) {
        echo "✓ SUCCESS - HTTP {$result['http_code']}\n";
        $data = json_decode($result['response'], true);
        echo "✓ Created contact with ID: {$data['id']}\n";
        echo "✓ Name: {$data['name_1']}\n";
        echo "✓ Email: {$data['mail']}\n";
        
        return $data;
    } else {
        echo "✗ FAILED - HTTP {$result['http_code']}\n";
        echo "✗ Error: {$result['error']}\n";
        echo "✗ Response: {$result['response']}\n";
        return false;
    }
}

// Main execution
echo "Bexio Contact API Simple Test\n";
echo "============================\n";

// Check if access token is set
if ($accessToken === 'YOUR_ACCESS_TOKEN_HERE') {
    echo "⚠️  Please update the \$accessToken variable with your actual Bexio access token!\n";
    echo "You can get it from:\n";
    echo "1. Bexio OAuth flow\n";
    echo "2. Personal Access Token from Bexio dashboard\n";
    echo "3. Your Laravel .env file (BEXIO_ACCESS_TOKEN)\n";
    exit(1);
}

echo "✓ Access token configured\n";
echo "✓ Base URL: {$baseUrl}\n";

// Run tests
$contacts = testGetContacts($accessToken, $baseUrl);
$newContact = testCreateContact($accessToken, $baseUrl);

echo "\n=== Test Summary ===\n";
echo "GET /contact: " . ($contacts !== false ? "✓ PASSED" : "✗ FAILED") . "\n";
echo "POST /contact: " . ($newContact !== false ? "✓ PASSED" : "✗ FAILED") . "\n";

echo "\n=== cURL Commands Used ===\n";
echo "GET:\n";
echo "curl -X GET \\\n";
echo "  {$baseUrl}/contact \\\n";
echo "  -H 'Accept: application/json' \\\n";
echo "  -H 'Authorization: Bearer {access-token}'\n";

echo "\nPOST:\n";
echo "curl -X POST \\\n";
echo "  {$baseUrl}/contact \\\n";
echo "  -H 'Content-Type: application/json' \\\n";
echo "  -H 'Accept: application/json' \\\n";
echo "  -H 'Authorization: Bearer {access-token}' \\\n";
echo "  -d '{\"contact_type_id\":1,\"name_1\":\"Kim Rebill Test Company\",...}'\n";

echo "\nDone!\n";
