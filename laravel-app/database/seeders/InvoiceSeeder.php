<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Invoice;
use App\Models\User;
use App\Models\RecurringTemplate;

class InvoiceSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get the demo user (or create one if doesn't exist)
        $user = User::where('email', '<EMAIL>')->first();

        if (!$user) {
            // Create demo organization first
            $organization = \App\Models\Organization::firstOrCreate([
                'bexio_org_id' => 'demo_org_123',
            ], [
                'name' => 'Demo Organization',
                'email' => '<EMAIL>',
                'country' => 'CH',
                'language' => 'en',
                'subscription_status' => 'trial',
                'subscription_model' => 'monthly',
                'trial_ends_at' => now()->addDays(90),
                'activated_at' => now(),
            ]);

            $user = User::create([
                'bexio_id' => '12345',
                'name' => 'Demo User',
                'email' => '<EMAIL>',
                'access_token' => 'mock_access_token',
                'refresh_token' => null,
                'token_expires_at' => now()->addHours(1),
                'organization_id' => $organization->id,
            ]);
        }

        // Create demo invoices
        $invoices = [
            [
                'user_id' => $user->id,
                'organization_id' => $user->organization_id,
                'bexio_id' => '1001',
                'title' => 'Monthly Consulting Services',
                'document_nr' => 'INV-2024-1001',
                'contact_info' => [
                    'name' => 'Acme Corporation',
                    'email' => '<EMAIL>',
                    'address' => '123 Business St, Zurich, Switzerland'
                ],
                'total' => 2500.00,
                'status' => 'active',
                'is_recurring' => true,
                'recurring_settings' => [
                    'interval' => 'monthly',
                    'next_charge' => now()->addMonth()->format('Y-m-d')
                ]
            ],
            [
                'user_id' => $user->id,
                'organization_id' => $user->organization_id,
                'bexio_id' => '1002',
                'title' => 'Software Development Services',
                'document_nr' => 'INV-2024-1002',
                'contact_info' => [
                    'name' => 'Tech Solutions GmbH',
                    'email' => '<EMAIL>',
                    'address' => '456 Innovation Ave, Basel, Switzerland'
                ],
                'total' => 1200.00,
                'status' => 'active',
                'is_recurring' => false,
                'recurring_settings' => null
            ],
            [
                'user_id' => $user->id,
                'organization_id' => $user->organization_id,
                'bexio_id' => '1003',
                'title' => 'Weekly Marketing Support',
                'document_nr' => 'INV-2024-1003',
                'contact_info' => [
                    'name' => 'StartupXYZ AG',
                    'email' => '<EMAIL>',
                    'address' => '789 Startup Blvd, Geneva, Switzerland'
                ],
                'total' => 850.00,
                'status' => 'draft',
                'is_recurring' => true,
                'recurring_settings' => [
                    'interval' => 'weekly',
                    'next_charge' => now()->addWeek()->format('Y-m-d')
                ]
            ],
            [
                'user_id' => $user->id,
                'organization_id' => $user->organization_id,
                'bexio_id' => '1004',
                'title' => 'Digital Marketing Campaign',
                'document_nr' => 'INV-2024-1004',
                'contact_info' => [
                    'name' => 'Digital Agency Ltd',
                    'email' => '<EMAIL>',
                    'address' => '321 Creative St, Bern, Switzerland'
                ],
                'total' => 3200.00,
                'status' => 'active',
                'is_recurring' => false,
                'recurring_settings' => null
            ],
            [
                'user_id' => $user->id,
                'organization_id' => $user->organization_id,
                'bexio_id' => '1005',
                'title' => 'Monthly Business Consulting',
                'document_nr' => 'INV-2024-1005',
                'contact_info' => [
                    'name' => 'Consulting Pro SA',
                    'email' => '<EMAIL>',
                    'address' => '654 Professional Way, Lausanne, Switzerland'
                ],
                'total' => 1800.00,
                'status' => 'active',
                'is_recurring' => true,
                'recurring_settings' => [
                    'interval' => 'monthly',
                    'next_charge' => now()->addDays(15)->format('Y-m-d')
                ]
            ]
        ];

        foreach ($invoices as $invoiceData) {
            $invoice = Invoice::create($invoiceData);

            // Create recurring template if it's a recurring invoice
            if ($invoice->is_recurring) {
                RecurringTemplate::create([
                    'organization_id' => $user->organization_id,
                    'invoice_id' => $invoice->id,
                    'interval' => $invoice->recurring_settings['interval'],
                    'next_run' => $invoice->recurring_settings['next_charge']
                ]);
            }
        }
    }
}
