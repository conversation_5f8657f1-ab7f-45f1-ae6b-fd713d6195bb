<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Update users table with all needed fields
        Schema::table('users', function (Blueprint $table) {
            // Make bexio_id nullable if not already
            if (Schema::hasColumn('users', 'bexio_id')) {
                $table->string('bexio_id')->nullable()->change();
            }

            // Admin fields
            if (!Schema::hasColumn('users', 'is_admin')) {
                $table->boolean('is_admin')->default(false)->after('email_verified_at');
            }
            if (!Schema::hasColumn('users', 'is_active')) {
                $table->boolean('is_active')->default(true)->after('is_admin');
            }
            if (!Schema::hasColumn('users', 'trial_ends_at')) {
                $table->date('trial_ends_at')->nullable()->after('is_active');
            }
            if (!Schema::hasColumn('users', 'subscription_status')) {
                $table->enum('subscription_status', ['trial', 'active', 'suspended', 'cancelled'])->default('trial')->after('trial_ends_at');
            }
            if (!Schema::hasColumn('users', 'last_login_at')) {
                $table->timestamp('last_login_at')->nullable()->after('subscription_status');
            }

            // Email template fields
            if (!Schema::hasColumn('users', 'email_subject')) {
                $table->string('email_subject')->nullable()->after('last_login_at');
            }
            if (!Schema::hasColumn('users', 'email_body')) {
                $table->text('email_body')->nullable()->after('email_subject');
            }
            if (!Schema::hasColumn('users', 'template_language')) {
                $table->string('template_language', 2)->default('EN')->after('email_body');
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // This migration adds fields, rollback would be complex
        // Better to use fresh migration if needed
    }
};
