<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // All columns already exist, skip migration
        // This migration was created but columns already exist from previous migrations
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Skip rollback since columns already existed
    }
};
