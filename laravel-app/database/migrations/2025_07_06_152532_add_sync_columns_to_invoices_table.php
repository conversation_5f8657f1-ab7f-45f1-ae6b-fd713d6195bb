<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('invoices', function (Blueprint $table) {
            // Add title column for invoice title/description
            $table->string('title')->nullable()->after('bexio_id');

            // Make bexio_id nullable for local-only invoices
            $table->string('bexio_id')->nullable()->change();

            // Add index for bexio_id for faster lookups during sync
            $table->index(['organization_id', 'bexio_id']);

            // Add last_synced_at timestamp for tracking sync status
            $table->timestamp('last_synced_at')->nullable()->after('updated_at');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('invoices', function (Blueprint $table) {
            $table->dropIndex(['organization_id', 'bexio_id']);
            $table->dropColumn(['title', 'last_synced_at']);
            $table->string('bexio_id')->nullable(false)->change();
        });
    }
};
