<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // First, add new enum values
        DB::statement("ALTER TABLE invoices MODIFY COLUMN status ENUM('draft','sent','paid','cancelled','active') DEFAULT 'draft'");

        // Update existing statuses to match Next.js structure
        DB::statement("UPDATE invoices SET status = 'active' WHERE status IN ('sent', 'paid')");
        DB::statement("UPDATE invoices SET status = 'draft' WHERE status = 'cancelled'");

        // Finally, change enum to only have 'draft' and 'active'
        DB::statement("ALTER TABLE invoices MODIFY COLUMN status ENUM('draft', 'active') DEFAULT 'draft'");
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Revert back to original enum
        DB::statement("ALTER TABLE invoices MODIFY COLUMN status ENUM('draft','sent','paid','cancelled') DEFAULT 'draft'");
    }
};
