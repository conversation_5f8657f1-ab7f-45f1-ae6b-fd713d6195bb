<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('invoices', function (Blueprint $table) {
            // Fields for recurring functionality
            $table->timestamp('last_processed_at')->nullable()->after('last_synced_at');
            $table->date('next_run_date')->nullable()->after('last_processed_at');
            $table->unsignedBigInteger('recurring_template_id')->nullable()->after('next_run_date');
            $table->boolean('created_from_recurring')->default(false)->after('recurring_template_id');

            // Add indexes for performance
            $table->index(['is_recurring', 'next_run_date']);
            $table->index(['created_from_recurring']);
            $table->index(['recurring_template_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('invoices', function (Blueprint $table) {
            $table->dropIndex(['is_recurring', 'next_run_date']);
            $table->dropIndex(['created_from_recurring']);
            $table->dropIndex(['recurring_template_id']);
            $table->dropColumn([
                'last_processed_at',
                'next_run_date',
                'recurring_template_id',
                'created_from_recurring'
            ]);
        });
    }
};
