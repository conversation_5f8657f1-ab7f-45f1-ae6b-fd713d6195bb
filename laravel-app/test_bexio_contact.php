<?php

require_once __DIR__ . '/vendor/autoload.php';

use Illuminate\Support\Facades\Http;
use GuzzleHttp\Client;

/**
 * Test Bexio Contact API
 * 
 * This script tests GET and POST operations for Bexio Contact API
 * using the access token from Bexio OAuth login
 */

// Configuration
$baseUrl = 'https://api.bexio.com/2.0';
$accessToken = null; // Will be set from environment or user input

/**
 * Get access token from environment or prompt user
 */
function getAccessToken() {
    global $accessToken;
    
    // Try to get from environment first
    if (file_exists(__DIR__ . '/.env')) {
        $envContent = file_get_contents(__DIR__ . '/.env');
        if (preg_match('/BEXIO_ACCESS_TOKEN=(.+)/', $envContent, $matches)) {
            $accessToken = trim($matches[1], '"\'');
            echo "✓ Access token loaded from .env file\n";
            return $accessToken;
        }
    }
    
    // Prompt user for access token
    echo "Please enter your Bexio access token: ";
    $handle = fopen("php://stdin", "r");
    $accessToken = trim(fgets($handle));
    fclose($handle);
    
    return $accessToken;
}

/**
 * Make HTTP request to Bexio API
 */
function makeRequest($method, $endpoint, $data = null) {
    global $baseUrl, $accessToken;
    
    $client = new Client();
    
    $options = [
        'headers' => [
            'Accept' => 'application/json',
            'Authorization' => 'Bearer ' . $accessToken,
        ],
        'timeout' => 30,
    ];
    
    if ($data && in_array($method, ['POST', 'PUT', 'PATCH'])) {
        $options['headers']['Content-Type'] = 'application/json';
        $options['json'] = $data;
    }
    
    try {
        $response = $client->request($method, $baseUrl . $endpoint, $options);
        return [
            'success' => true,
            'status' => $response->getStatusCode(),
            'data' => json_decode($response->getBody()->getContents(), true),
            'headers' => $response->getHeaders()
        ];
    } catch (\GuzzleHttp\Exception\RequestException $e) {
        $response = $e->getResponse();
        $statusCode = $response ? $response->getStatusCode() : 0;
        $body = $response ? $response->getBody()->getContents() : $e->getMessage();
        
        return [
            'success' => false,
            'status' => $statusCode,
            'error' => $body,
            'message' => $e->getMessage()
        ];
    }
}

/**
 * Test GET /contact - Retrieve all contacts
 */
function testGetContacts() {
    echo "\n" . str_repeat("=", 50) . "\n";
    echo "Testing GET /contact - Retrieve all contacts\n";
    echo str_repeat("=", 50) . "\n";
    
    $result = makeRequest('GET', '/contact');
    
    if ($result['success']) {
        echo "✓ SUCCESS - Status: {$result['status']}\n";
        echo "✓ Retrieved " . count($result['data']) . " contacts\n";
        
        // Show first contact as example
        if (!empty($result['data'])) {
            echo "\nFirst contact example:\n";
            $firstContact = $result['data'][0];
            echo "- ID: {$firstContact['id']}\n";
            echo "- Name: {$firstContact['name_1']} {$firstContact['name_2']}\n";
            echo "- Email: {$firstContact['mail']}\n";
            echo "- Type: {$firstContact['contact_type_id']}\n";
        }
        
        return $result['data'];
    } else {
        echo "✗ FAILED - Status: {$result['status']}\n";
        echo "✗ Error: {$result['error']}\n";
        return false;
    }
}

/**
 * Test POST /contact - Create new contact
 */
function testCreateContact() {
    echo "\n" . str_repeat("=", 50) . "\n";
    echo "Testing POST /contact - Create new contact\n";
    echo str_repeat("=", 50) . "\n";
    
    // Sample contact data based on the curl example
    $contactData = [
        'contact_type_id' => 1,
        'name_1' => 'Test Company ' . date('Y-m-d H:i:s'),
        'salutation_id' => 2,
        'address' => 'Test Street 22',
        'street_name' => 'Test Street',
        'house_number' => '22',
        'address_addition' => 'Building A',
        'postcode' => '8004',
        'city' => 'Zurich',
        'country_id' => 1,
        'mail' => 'test' . time() . '@example.org',
        'mail_second' => '',
        'phone_fixed' => '+41 44 123 45 67',
        'phone_fixed_second' => '',
        'phone_mobile' => '+41 79 123 45 67',
        'fax' => '',
        'url' => 'https://example.org',
        'skype_name' => '',
        'remarks' => 'Created by Kim Rebill test script',
        'contact_group_ids' => '1,2',
        'user_id' => 1,
        'owner_id' => 1
    ];
    
    echo "Creating contact with data:\n";
    echo "- Name: {$contactData['name_1']}\n";
    echo "- Email: {$contactData['mail']}\n";
    echo "- Address: {$contactData['address']}, {$contactData['city']}\n";
    
    $result = makeRequest('POST', '/contact', $contactData);
    
    if ($result['success']) {
        echo "✓ SUCCESS - Status: {$result['status']}\n";
        echo "✓ Contact created with ID: {$result['data']['id']}\n";
        echo "✓ Name: {$result['data']['name_1']} {$result['data']['name_2']}\n";
        echo "✓ Email: {$result['data']['mail']}\n";
        
        return $result['data'];
    } else {
        echo "✗ FAILED - Status: {$result['status']}\n";
        echo "✗ Error: {$result['error']}\n";
        return false;
    }
}

/**
 * Test GET /contact/{id} - Retrieve specific contact
 */
function testGetSpecificContact($contactId) {
    echo "\n" . str_repeat("=", 50) . "\n";
    echo "Testing GET /contact/{$contactId} - Retrieve specific contact\n";
    echo str_repeat("=", 50) . "\n";
    
    $result = makeRequest('GET', "/contact/{$contactId}");
    
    if ($result['success']) {
        echo "✓ SUCCESS - Status: {$result['status']}\n";
        echo "✓ Contact details:\n";
        echo "  - ID: {$result['data']['id']}\n";
        echo "  - Name: {$result['data']['name_1']} {$result['data']['name_2']}\n";
        echo "  - Email: {$result['data']['mail']}\n";
        echo "  - Phone: {$result['data']['phone_fixed']}\n";
        echo "  - Address: {$result['data']['address']}, {$result['data']['city']}\n";
        
        return $result['data'];
    } else {
        echo "✗ FAILED - Status: {$result['status']}\n";
        echo "✗ Error: {$result['error']}\n";
        return false;
    }
}

/**
 * Main test execution
 */
function runTests() {
    echo "Bexio Contact API Testing\n";
    echo "========================\n";
    
    // Get access token
    $token = getAccessToken();
    if (!$token) {
        echo "✗ No access token provided. Exiting.\n";
        return;
    }
    
    echo "✓ Access token configured\n";
    echo "✓ Base URL: https://api.bexio.com/2.0\n";
    
    // Test 1: Get all contacts
    $contacts = testGetContacts();
    
    // Test 2: Create new contact
    $newContact = testCreateContact();
    
    // Test 3: Get specific contact (if we created one)
    if ($newContact && isset($newContact['id'])) {
        testGetSpecificContact($newContact['id']);
    }
    
    echo "\n" . str_repeat("=", 50) . "\n";
    echo "Testing completed!\n";
    echo str_repeat("=", 50) . "\n";
}

// Run the tests
runTests();
