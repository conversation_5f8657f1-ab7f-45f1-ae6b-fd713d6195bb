<?php

require_once __DIR__ . '/vendor/autoload.php';

use Illuminate\Foundation\Application;

// Bootstrap Laravel
$app = require_once __DIR__ . '/bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

echo "📧 Testing Email Invoice with Real Contact\n";
echo "=========================================\n\n";

// Get authenticated user
$user = \App\Models\User::first();
if (!$user) {
    echo "❌ Error: No user found in database\n";
    exit(1);
}

// Authenticate user for API calls
\Illuminate\Support\Facades\Auth::login($user);

echo "✅ User authenticated: {$user->name}\n";
echo "📧 Target email: <EMAIL>\n";
echo "🏢 Contact ID: 4 (Doni <PERSON>ur)\n\n";

try {
    // Create invoice with existing contact ID 4
    echo "📄 Creating invoice with contact ID 4...\n";

    $invoice = \App\Models\Invoice::create([
        'user_id' => $user->id,
        'title' => 'Email Test Invoice - Final',
        'document_nr' => 'INV-FINAL-TEST-' . date('Ymd-His'),
        'contact_info' => json_encode([
            'contact_id' => 4, // Store contact ID in JSON
            'name' => 'Doni Bageur',
            'email' => '<EMAIL>'
        ]),
        'total' => 900.00,
        'status' => 'active',
        'is_recurring' => false,
        'items' => json_encode([
            [
                'name' => 'Final Email Test Service',
                'quantity' => 6,
                'unit_price' => 150,
                'unit_id' => 2, // Hours
                'tax_id' => 3   // UEX - Export/Exempt 0.00%
            ]
        ]),
        'tax_status' => 0,
        'organization_id' => $user->organization_id ?? 1
    ]);

    echo "✅ Invoice created successfully!\n";
    echo "   - Invoice ID: {$invoice->id}\n";
    echo "   - Document Nr: {$invoice->document_nr}\n";
    echo "   - Total: CHF " . number_format($invoice->total, 2) . "\n";

    $contactInfo = json_decode($invoice->contact_info, true);
    echo "   - Contact ID: {$contactInfo['contact_id']}\n";
    echo "   - Contact Name: {$contactInfo['name']}\n\n";

    // Create controller instance
    $controller = new \App\Http\Controllers\Api\BexioApiController();

    // Step 1: Create invoice in Bexio
    echo "🔄 Step 1: Creating invoice in Bexio...\n";

    $invoiceRequest = new \Illuminate\Http\Request();
    $invoiceRequest->merge(['invoice_id' => $invoice->id]);

    $invoiceResponse = $controller->createInvoice($invoiceRequest);
    $invoiceResult = json_decode($invoiceResponse->getContent(), true);

    if (isset($invoiceResult['success']) && $invoiceResult['success']) {
        echo "✅ Invoice created in Bexio!\n";
        echo "   - Bexio Invoice ID: {$invoiceResult['bexio_id']}\n\n";

        // Refresh invoice data
        $invoice->refresh();

        // Step 2: Send email
        echo "📧 Step 2: Sending invoice email...\n";
        echo "   Note: Trial limitation - sending to user email: {$user->email}\n";

        $emailRequest = new \Illuminate\Http\Request();
        $emailRequest->merge([
            'invoice_id' => $invoice->id,
            'recipient_email' => $user->email // Use user's email due to trial limitation
        ]);

        $emailResponse = $controller->sendInvoiceEmail($emailRequest);
        $emailResult = json_decode($emailResponse->getContent(), true);

        if (isset($emailResult['success']) && $emailResult['success']) {
            echo "✅ SUCCESS! Invoice email sent!\n";
            echo "   - Recipient: {$user->email}\n";
            echo "   - Bexio Invoice ID: {$invoice->bexio_id}\n\n";

            // Show email template preview
            echo "📄 Email Template Preview:\n";
            echo str_repeat('=', 60) . "\n";

            $template = \App\Helpers\EmailTemplateHelper::getUserTemplate($user);

            $templateData = [
                'total' => number_format($invoice->total, 2),
                'date' => now()->format('M j, Y'),
                'valid_until' => now()->addDays(30)->format('M j, Y'),
                'document_number' => $invoice->document_nr,
                'title' => $invoice->title,
                'currency' => 'CHF',
                'name' => 'Doni Bageur',
                'name_1' => 'Doni Bageur',
                'user' => $user->name,
                'user_email' => $user->email,
                'company_name' => $user->organization->name ?? 'Your Company',
                'network_link' => "https://office.bexio.com/index.php/kb_invoice/show/id/{$invoice->bexio_id}"
            ];

            $subject = \App\Helpers\EmailTemplateHelper::replaceVariables($template['subject'], $templateData);
            $body = \App\Helpers\EmailTemplateHelper::replaceVariables($template['body'], $templateData);

            echo "📧 Subject: {$subject}\n\n";
            echo "📝 Body:\n";
            echo str_repeat('-', 40) . "\n";
            echo $body . "\n";
            echo str_repeat('-', 40) . "\n\n";

            echo "🎉 COMPLETE SUCCESS!\n";
            echo "✅ Invoice created in Bexio\n";
            echo "✅ Email <NAME_EMAIL>\n";
            echo "✅ Check your email inbox!\n\n";

        } else {
            echo "❌ Failed to send email\n";
            echo "   - Error: " . ($emailResult['error'] ?? 'Unknown error') . "\n\n";
        }

    } else {
        echo "❌ Failed to create invoice in Bexio\n";
        echo "   - Error: " . ($invoiceResult['error'] ?? 'Unknown error') . "\n\n";

        // Show debug info
        echo "🔍 Debug Info:\n";
        $contactInfo = json_decode($invoice->contact_info, true);
        echo "   - Contact Info: " . json_encode($contactInfo) . "\n";
        echo "   - Invoice Items: " . json_encode($invoice->items) . "\n\n";
    }

} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n\n";
}

echo "📊 Final Summary:\n";
echo "================\n";
echo "1. ✅ Contact exists in Bexio (ID: 4, <EMAIL>)\n";
echo "2. ✅ Invoice created in Kim Rebill database\n";
echo "3. 🔄 Invoice creation in Bexio (check results above)\n";
echo "4. 📧 Email sending (check results above)\n";
echo "\n💡 If successful, check <EMAIL> for the invoice email!\n";
