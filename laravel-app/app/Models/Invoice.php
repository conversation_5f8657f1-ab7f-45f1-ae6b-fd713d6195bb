<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Invoice extends Model
{
    protected $fillable = [
        'user_id',
        'organization_id',
        'bexio_id',
        'title',
        'document_nr',
        'contact_info',
        'total',
        'status',
        'is_recurring',
        'recurring_settings',
        'items',
        'tax_status',
        'last_synced_at',
        'bexio_created_at',
        'last_processed_at',
        'next_run_date',
        'recurring_template_id',
        'created_from_recurring'
    ];

    protected $casts = [
        'contact_info' => 'array',
        'recurring_settings' => 'array',
        'items' => 'array',
        'is_recurring' => 'boolean',
        'created_from_recurring' => 'boolean',
        'total' => 'decimal:2',
        'tax_status' => 'integer',
        'last_synced_at' => 'datetime',
        'bexio_created_at' => 'datetime',
        'last_processed_at' => 'datetime',
        'next_run_date' => 'date'
    ];

    // Relationships
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function organization()
    {
        return $this->belongsTo(Organization::class);
    }

    public function recurringTemplate()
    {
        return $this->hasOne(RecurringTemplate::class);
    }

    // Scopes
    public function scopeForOrganization($query, $organizationId)
    {
        return $query->where('organization_id', $organizationId);
    }

    public function scopeRecurring($query)
    {
        return $query->where('is_recurring', true);
    }

    public function scopeRecurringTemplates($query)
    {
        return $query->where('is_recurring', true)->where('created_from_recurring', false);
    }

    public function scopeGeneratedFromRecurring($query)
    {
        return $query->where('created_from_recurring', true);
    }

    public function scopeDueForProcessing($query)
    {
        return $query->where('is_recurring', true)
                    ->where('status', 'active')
                    ->where(function($q) {
                        $q->whereNull('next_run_date')
                          ->orWhere('next_run_date', '<=', now()->toDateString());
                    });
    }

    public function scopeByStatus($query, $status)
    {
        return $query->where('status', $status);
    }

    // Helper Methods
    public function isPaid()
    {
        return $this->status === 'paid';
    }

    public function isDraft()
    {
        return $this->status === 'draft';
    }

    public function isSent()
    {
        return $this->status === 'sent';
    }

    public function isCancelled()
    {
        return $this->status === 'cancelled';
    }

    // Sync-related methods
    public function isFromBexio()
    {
        return !is_null($this->bexio_id);
    }

    public function needsSync()
    {
        if (!$this->isFromBexio()) {
            return false;
        }

        // Check if never synced or synced more than 1 hour ago
        return is_null($this->last_synced_at) ||
               $this->last_synced_at->lt(now()->subHour());
    }

    public function markAsSynced()
    {
        $this->update(['last_synced_at' => now()]);
    }

    // Scope for Bexio invoices
    public function scopeFromBexio($query)
    {
        return $query->whereNotNull('bexio_id');
    }

    public function scopeLocalOnly($query)
    {
        return $query->whereNull('bexio_id');
    }

    // Recurring-related helper methods
    public function isRecurringTemplate()
    {
        return $this->is_recurring && !$this->created_from_recurring;
    }

    public function isGeneratedFromRecurring()
    {
        return $this->created_from_recurring;
    }

    public function isDueForProcessing()
    {
        if (!$this->is_recurring || $this->status !== 'active') {
            return false;
        }

        return is_null($this->next_run_date) ||
               $this->next_run_date <= now()->toDateString();
    }

    public function calculateNextRunDate()
    {
        if (!$this->recurring_settings || !isset($this->recurring_settings['interval'])) {
            return null;
        }

        $interval = $this->recurring_settings['interval'];
        $baseDate = $this->last_processed_at ?? $this->created_at;

        switch ($interval) {
            case 'daily':
                return $baseDate->addDay();
            case 'weekly':
                return $baseDate->addWeek();
            case 'monthly':
                return $baseDate->addMonth();
            case 'yearly':
                return $baseDate->addYear();
            default:
                return null;
        }
    }

    public function markAsProcessed()
    {
        $this->update([
            'last_processed_at' => now(),
            'next_run_date' => $this->calculateNextRunDate()
        ]);
    }

    // Relationship to parent template (for generated invoices)
    public function parentTemplate()
    {
        return $this->belongsTo(Invoice::class, 'recurring_template_id');
    }

    // Relationship to generated invoices (for templates)
    public function generatedInvoices()
    {
        return $this->hasMany(Invoice::class, 'recurring_template_id');
    }
}
