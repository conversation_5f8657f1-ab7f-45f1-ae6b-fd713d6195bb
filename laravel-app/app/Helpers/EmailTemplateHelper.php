<?php

namespace App\Helpers;

class EmailTemplateHelper
{
    /**
     * Get base email template by language
     * 
     * @param string $language
     * @return array
     */
    public static function getBaseTemplate(string $language = 'EN'): array
    {
        $templates = [
            'EN' => [
                'subject' => 'RE: Invoice',
                'body' => "Dear Sir or <PERSON><PERSON>\n\nWe thank you for your order. Our services are calculated as follows:\n\nDate: [Date]\nAmount: [Currency] [Total]\nDue date: [Valid Until]\n\nPlease use the following link to view and pay the invoice:\n[Network Link]\n\nPlease pay by means of one of the available payment methods.\nIf you have any questions regarding this invoice, please do not hesitate to contact us at any time.\n\nWarm regards\n\n[Company Name]"
            ],
            'DE' => [
                'subject' => 'RE: Rechnung',
                'body' => "Sehr geehrte Damen und Herren\n\nWir danken für Ihren Auftrag und berechnen unsere Leistungen wie folgt:\n\nDatum: [Date]\nBetrag: [Currency] [Total]\nZahlbar bis: [Valid Until]\n\nUnter folgendem Link können Sie die Rechnung ansehen und bezahlen:\n[Network Link]\n\nWir bitten um Bezahlung über einer der zur Verfügung stehenden Zahlungsmöglichkeiten.\nFür Rückfragen zu dieser Rechnung stehen wir jederzeit gerne zur Verfügung.\n\nFreundliche Grüsse\n\n[Company Name]"
            ],
            'FR' => [
                'subject' => 'RE: Facture',
                'body' => "Madame, Monsieur,\n\nNous vous remercions de votre commande. Nos services vous seront facturés comme suit:\n\nDate: [Date]\nMontant: [Currency] [Total]\nPayable jusqu'au: [Valid Until]\n\nCliquez sur le lien suivant pour consulter et régler votre facture:\n[Network Link]\n\nNous vous prions de bien vouloir effectuer votre paiement selon l'un des modes de paiement disponibles.\nN'hésitez pas à nous contacter si vous avez des questions concernant cette facture.\n\nCordialement,\n\n[Company Name]"
            ]
        ];

        return $templates[strtoupper($language)] ?? $templates['EN'];
    }

    /**
     * Get user's email template or fallback to base template
     * 
     * @param \App\Models\User $user
     * @return array
     */
    public static function getUserTemplate($user): array
    {
        $baseTemplate = self::getBaseTemplate($user->template_language ?? 'EN');
        
        return [
            'subject' => $user->email_subject ?? $baseTemplate['subject'],
            'body' => $user->email_body ?? $baseTemplate['body'],
            'language' => $user->template_language ?? 'EN'
        ];
    }

    /**
     * Get available template variables
     * 
     * @return array
     */
    public static function getAvailableVariables(): array
    {
        return [
            '[Total]' => 'Total amount',
            '[Date]' => 'Invoice date',
            '[Valid Until]' => 'Expiry date',
            '[Document Number]' => 'Invoice number',
            '[Project]' => 'Project name',
            '[Title]' => 'Invoice title',
            '[Currency]' => 'Currency code',
            '[Name 1]' => 'Last name / Company',
            '[Name]' => 'Full name',
            '[Name 2]' => 'First name / Company',
            '[User]' => 'User name',
            '[User Email]' => 'User email',
            '[Company Name]' => 'Organization',
            '[Company Phone Nr]' => 'Phone number',
            '[Website]' => 'Website URL',
            '[Network Link]' => 'Invoice link'
        ];
    }

    /**
     * Replace template variables with actual values
     * 
     * @param string $template
     * @param array $data
     * @return string
     */
    public static function replaceVariables(string $template, array $data): string
    {
        $variables = [
            '[Total]' => $data['total'] ?? '',
            '[Date]' => $data['date'] ?? '',
            '[Valid Until]' => $data['valid_until'] ?? '',
            '[Document Number]' => $data['document_number'] ?? '',
            '[Project]' => $data['project'] ?? '',
            '[Title]' => $data['title'] ?? '',
            '[Currency]' => $data['currency'] ?? 'CHF',
            '[Name 1]' => $data['name_1'] ?? '',
            '[Name]' => $data['name'] ?? '',
            '[Name 2]' => $data['name_2'] ?? '',
            '[User]' => $data['user'] ?? '',
            '[User Email]' => $data['user_email'] ?? '',
            '[Company Name]' => $data['company_name'] ?? '',
            '[Company Phone Nr]' => $data['company_phone'] ?? '',
            '[Website]' => $data['website'] ?? '',
            '[Network Link]' => $data['network_link'] ?? ''
        ];

        return str_replace(array_keys($variables), array_values($variables), $template);
    }
}
