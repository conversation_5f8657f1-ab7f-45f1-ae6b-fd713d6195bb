<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use App\Models\User;

class SettingsController extends Controller
{
    public function index()
    {
        $user = Auth::user();
        return view('settings.index', compact('user'));
    }

    public function updateProfile(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|email|max:255|unique:users,email,' . Auth::id(),
        ]);

        $user = Auth::user();
        $user->update([
            'name' => $request->name,
            'email' => $request->email,
        ]);

        return redirect()->route('settings.index')->with('success', 'Profile updated successfully!');
    }

    public function updateTemplates(Request $request)
    {
        $request->validate([
            'template_language' => 'required|in:EN,DE,FR',
            'email_subject' => 'required|string|max:255',
            'email_body' => 'required|string|max:5000',
        ]);

        $user = Auth::user();
        $user->update([
            'template_language' => $request->template_language,
            'email_subject' => $request->email_subject,
            'email_body' => $request->email_body,
        ]);

        return redirect()->route('settings.index')->with('success', 'Email templates updated successfully!');
    }

    public function updatePassword(Request $request)
    {
        $request->validate([
            'current_password' => 'required',
            'new_password' => 'required|min:8|confirmed',
        ]);

        $user = Auth::user();

        if (!Hash::check($request->current_password, $user->password)) {
            return back()->withErrors(['current_password' => 'Current password is incorrect.']);
        }

        $user->update([
            'password' => Hash::make($request->new_password),
        ]);

        return redirect()->route('settings.index')->with('success', 'Password updated successfully!');
    }

    public function updateBexioSettings(Request $request)
    {
        $request->validate([
            'bexio_access_token' => 'nullable|string',
            'bexio_refresh_token' => 'nullable|string',
        ]);

        $user = Auth::user();
        $user->update([
            'bexio_access_token' => $request->bexio_access_token,
            'bexio_refresh_token' => $request->bexio_refresh_token,
            'bexio_token_expires_at' => $request->bexio_access_token ? now()->addHour() : null,
        ]);

        return redirect()->route('settings.index')->with('success', 'Bexio settings updated successfully!');
    }

    public function resetBexioConnection()
    {
        $user = Auth::user();
        $user->update([
            'bexio_access_token' => null,
            'bexio_refresh_token' => null,
            'bexio_token_expires_at' => null,
        ]);

        return redirect()->route('bexio.login')->with('info', 'Please reconnect your Bexio account.');
    }
}
