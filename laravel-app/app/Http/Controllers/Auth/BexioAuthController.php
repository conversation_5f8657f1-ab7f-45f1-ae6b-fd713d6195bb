<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Models\Organization;
use App\Models\Subscription;
use App\Services\BexioServiceInterface;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;
use <PERSON><PERSON>jett\OpenIDConnectClient;
use GuzzleHttp\Client;
use GuzzleHttp\Exception\RequestException;

class BexioAuthController extends Controller
{
    public function __construct(
        private BexioServiceInterface $bexioService
    ) {}

    public function showLoginForm()
    {
        return view('auth.login');
    }

    public function redirect()
    {
        try {
            $clientId = config('bexio.client_id');
            $clientSecret = config('bexio.client_secret');

            if (!$clientId || !$clientSecret) {
                Log::error('Bexio client credentials not configured');
                return redirect()->route('login')->withErrors(['auth' => 'Bexio client credentials not configured']);
            }

            $oidc = new OpenIDConnectClient(
                config('bexio.auth_base_url'),
                $clientId,
                $clientSecret
            );

            $oidc->setRedirectURL(config('bexio.redirect_uri'));
            $oidc->addScope(['openid', 'profile', 'email', 'accounting']);

            $oidc->authenticate();

        } catch (\Exception $e) {
            Log::error('Bexio OAuth redirect failed: ' . $e->getMessage());
            return redirect()->route('login')->withErrors(['auth' => 'Authentication failed']);
        }
    }

    public function callback()
    {
        // Check for OAuth errors first
        if (request('error')) {
            Log::error('OAuth error received: ' . request('error'));
            return redirect('/login')->withErrors([
                'bexio' => 'OAuth error: ' . request('error')
            ]);
        }

        try {
            // Use OpenIDConnectClient like PHP original
            $clientId = config('bexio.client_id');
            $clientSecret = config('bexio.client_secret');

            if (!$clientId || !$clientSecret) {
                Log::error('Bexio client credentials not configured');
                return redirect()->route('login')->withErrors(['auth' => 'Bexio client credentials not configured']);
            }

            $oidc = new OpenIDConnectClient(
                config('bexio.auth_base_url'),
                $clientId,
                $clientSecret
            );

            $oidc->setRedirectURL(config('bexio.redirect_uri'));
            $oidc->addScope(config('bexio.scopes'));

            // Configure endpoints from config
            $oidc->providerConfigParam([
                'authorization_endpoint' => config('bexio.auth_endpoint'),
                'token_endpoint' => config('bexio.token_endpoint'),
                'userinfo_endpoint' => config('bexio.userinfo_endpoint'),
            ]);

            // Authenticate and get tokens
            $oidc->authenticate();

            $accessToken = $oidc->getAccessToken();
            $refreshToken = $oidc->getRefreshToken();
            $expiresIn = 3600; // bexio default (1h)

            if (!$accessToken) {
                return redirect('/login')->withErrors([
                    'bexio' => 'Failed to obtain access token'
                ]);
            }

            // Get user info from OIDC
            $userinfo = $oidc->requestUserInfo();

            // Get company profile using Guzzle HTTP client
            $client = new Client();
            try {
                $response = $client->get(config('bexio.api_base_url') . '/3.0/company_profile', [
                    'headers' => [
                        'Authorization' => 'Bearer ' . $accessToken,
                        'Accept' => 'application/json'
                    ]
                ]);

                $profiles = json_decode($response->getBody()->getContents(), true);
            } catch (RequestException $e) {
                Log::error('Failed to fetch company profile: ' . $e->getMessage());
                $profiles = [];
            }
            $profile = is_array($profiles) && isset($profiles[0]) ? $profiles[0] : [];

            // Extract data like PHP original
            $companyId = $userinfo->company_id ?? '';
            $companyName = $profile['name'] ?? '';
            $address = $profile['address'] ?? '';
            $zip = $profile['postcode'] ?? '';
            $city = $profile['city'] ?? '';
            $email = $profile['mail'] ?? '';
            $phone = $profile['phone_fixed'] ?? '';

            // Contact person from userinfo
            $contactName = trim(($userinfo->given_name ?? '') . ' ' . ($userinfo->family_name ?? ''));

            $companyData = [
                'id' => $companyId,
                'name' => $companyName,
                'address' => $address,
                'postcode' => $zip,
                'city' => $city,
                'mail' => $email,
                'phone_fixed' => $phone,
                'contact_name' => $contactName,
            ];

            $userData = [
                'id' => $userinfo->sub ?? '',
                'name' => trim(($userinfo->given_name ?? '') . ' ' . ($userinfo->family_name ?? '')),
                'email' => $userinfo->email ?? '',
                'given_name' => $userinfo->given_name ?? '',
                'family_name' => $userinfo->family_name ?? '',
            ];

            $tokenData = [
                'access_token' => $accessToken,
                'refresh_token' => $refreshToken,
                'expires_in' => $expiresIn,
            ];

            return DB::transaction(function () use ($tokenData, $userData, $companyData, $refreshToken) {
                // Create or update organization
                $organization = Organization::updateOrCreate(
                    ['bexio_org_id' => $companyData['id']],
                    [
                        'name' => $companyData['name'],
                        'email' => $companyData['mail'],
                        'address' => $companyData['address'],
                        'zip' => $companyData['postcode'],
                        'city' => $companyData['city'],
                        'contact_name' => $companyData['contact_name'],
                        'phone' => $companyData['phone_fixed'],
                        'refresh_token' => $refreshToken,
                        'bexio_company_profile' => $companyData,
                        'subscription_status' => 'trial',
                        'subscription_start' => now()->toDateString(),
                    ]
                );

                // Set trial period for new organizations
                if ($organization->wasRecentlyCreated) {
                    $organization->update([
                        'trial_ends_at' => now()->addMonths(3),
                    ]);
                }

                // Check if this is the first user for this organization
                // Use direct SQL query like PHP original to avoid race conditions
                $existingUsersCount = DB::selectOne(
                    "SELECT COUNT(*) as count FROM users WHERE organization_id = ?",
                    [$organization->id]
                )->count;

                $isFirstUser = $existingUsersCount == 0;

                Log::info("Admin assignment debug", [
                    'organization_id' => $organization->id,
                    'existing_users_count' => $existingUsersCount,
                    'is_first_user' => $isFirstUser,
                    'bexio_user_id' => $userData['id']
                ]);

                // Find existing user or create new one  approach
                $user = User::where('bexio_id', $userData['id'])
                           ->where('organization_id', $organization->id)
                           ->first();

                if (!$user) {
                    // Create new user - first user gets admin privileges
                    $user = User::create([
                        'bexio_id' => $userData['id'],
                        'organization_id' => $organization->id,
                        'name' => $userData['name'],
                        'email' => $userData['email'],
                        'role' => 'user',
                        'access_token' => $tokenData['access_token'],
                        'refresh_token' => $tokenData['refresh_token'] ?? null,
                        'token_expires_at' => isset($tokenData['expires_in'])
                            ? now()->addSeconds($tokenData['expires_in'])
                            : now()->addHour(),
                        'refresh_token_rotated_at' => now(),
                        'last_login_at' => now(),
                        'bexio_user_profile' => $userData,
                        'bexio_company_id' => $companyData['id'],
                        'bexio_user_id' => $userData['id'],
                    ]);
                } else {
                    // Update existing user - keep existing admin status
                    $user->update([
                        'name' => $userData['name'],
                        'email' => $userData['email'],
                        'access_token' => $tokenData['access_token'],
                        'refresh_token' => $tokenData['refresh_token'] ?? null,
                        'token_expires_at' => isset($tokenData['expires_in'])
                            ? now()->addSeconds($tokenData['expires_in'])
                            : now()->addHour(),
                        'refresh_token_rotated_at' => now(),
                        'last_login_at' => now(),
                        'bexio_user_profile' => $userData,
                        'bexio_company_id' => $companyData['id'],
                        'bexio_user_id' => $userData['id'],
                    ]);
                }

                Log::info("User created/updated", [
                    'user_id' => $user->id,
                    'is_admin' => $user->is_admin,
                    'role' => $user->role,
                    'was_recently_created' => $user->wasRecentlyCreated
                ]);

                // Create subscription if it doesn't exist
                if (!$organization->subscription) {
                    Subscription::create([
                        'organization_id' => $organization->id,
                        'plan_type' => 'monthly',
                        'price' => 29.00, // Default monthly price
                        'status' => 'trial',
                        'trial_ends_at' => $organization->trial_ends_at,
                    ]);
                }

                Auth::login($user);

                // Send welcome email for new organizations
                if ($organization->wasRecentlyCreated) {
                    $this->sendWelcomeEmail($user, $organization);
                }

                return redirect('/dashboard');
            });

        } catch (\Exception $e) {
            Log::error('Bexio authentication failed: ' . $e->getMessage());
            return redirect('/login')->withErrors([
                'bexio' => 'Failed to authenticate with Bexio'
            ]);
        }
    }

    public function logout()
    {
        Auth::logout();
        return redirect('/');
    }



    private function sendWelcomeEmail($user, $organization)
    {
        // TODO: Implement welcome email sending
        Log::info("Welcome email should be sent to {$user->email} for organization {$organization->name}");
    }

    /**
     * Show admin login form
     */
    public function showAdminLoginForm()
    {
        return view('admin.login');
    }

    /**
     * Handle admin login (email/password)
     */
    public function adminLogin(Request $request)
    {
        $request->validate([
            'email' => 'required|email',
            'password' => 'required',
        ]);

        $credentials = $request->only('email', 'password');
        $remember = $request->boolean('remember');

        if (Auth::attempt($credentials, $remember)) {
            $user = Auth::user();

            // Check if user is admin
            if (!$user->is_admin) {
                Auth::logout();
                return back()->withErrors([
                    'email' => 'Access denied. Admin privileges required.',
                ])->onlyInput('email');
            }

            // Update last login timestamp
            User::where('id', $user->id)->update(['last_login_at' => now()]);

            Log::info("Admin login successful", [
                'user_id' => $user->id,
                'email' => $user->email
            ]);

            $request->session()->regenerate();

            return redirect()->intended('/admin');
        }

        Log::warning("Admin login failed", [
            'email' => $request->email,
            'ip' => $request->ip()
        ]);

        return back()->withErrors([
            'email' => 'The provided credentials do not match our records.',
        ])->onlyInput('email');
    }
}
