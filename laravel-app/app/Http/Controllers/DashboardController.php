<?php

namespace App\Http\Controllers;

use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use App\Services\BexioServiceInterface;
use App\Services\BexioSyncService;
use App\Models\Invoice;

class DashboardController extends Controller
{
    protected BexioServiceInterface $bexioService;
    protected BexioSyncService $syncService;

    public function __construct(BexioServiceInterface $bexioService, BexioSyncService $syncService)
    {
        $this->bexioService = $bexioService;
        $this->syncService = $syncService;
    }

    public function index()
    {
        $user = Auth::user();
        $organization = $user->organization;

        // Check if user has valid Bexio access token
        if (!$user->access_token) {
            Log::warning("User {$user->id} has no Bexio access token for dashboard");
            return $this->fallbackToLocalData($organization);
        }

        // Set access token for Bexio service
        $this->bexioService->setAccessToken($user->access_token);

        try {
            // <PERSON>'s main feature is recurring invoices - focus on local Kim data
            // Only sync status of <PERSON>'s recurring invoices with Bexio (not import all Bexio invoices)
            $this->syncKimRecurringInvoicesStatus($organization, $user->access_token);

            // Get Kim's recurring invoices from local database
            $kimInvoices = Invoice::forOrganization($organization->id)->get();
            $recurringInvoices = $kimInvoices->where('is_recurring', true);
            $drafts = $kimInvoices->where('status', 'draft');

            // Calculate metrics for Kim's invoices only
            // In Next.js context, "unpaid" means invoices that need payment (active recurring invoices)
            $unpaidInvoices = $kimInvoices->where('status', 'active')->where('is_recurring', true)->count();
            $totalRevenue = $kimInvoices->where('status', 'active')->sum('total');

            Log::info("Dashboard loaded with Kim's recurring invoices for user {$user->id}");

            return view('dashboard', [
                'invoices' => $kimInvoices,
                'recurringInvoices' => $recurringInvoices,
                'drafts' => $drafts,
                'unpaidInvoices' => $unpaidInvoices,
                'totalRevenue' => $totalRevenue,
                'dataSource' => 'kim_recurring',
                'totalRecurringInvoices' => $recurringInvoices->count()
            ]);

        } catch (\Exception $e) {
            Log::error("Failed to load Bexio data for dashboard: " . $e->getMessage());

            // Fallback to local database data
            return $this->fallbackToLocalData($organization);
        }
    }



    /**
     * Fallback to local database data when Bexio API is unavailable
     */
    private function fallbackToLocalData($organization): \Illuminate\View\View
    {
        Log::info("Using fallback local data for organization {$organization->id}");

        // Get invoices from local database
        $invoices = Invoice::where('organization_id', $organization->id)->get();

        // Get recurring invoices (invoices marked as recurring)
        $recurringInvoices = $invoices->where('is_recurring', true);

        // Get draft invoices
        $drafts = $invoices->where('status', 'draft');

        return view('dashboard', [
            'invoices' => $invoices,
            'recurringInvoices' => $recurringInvoices,
            'drafts' => $drafts,
            'unpaidInvoices' => $invoices->where('status', 'active')->where('is_recurring', true)->count(),
            'totalRevenue' => $invoices->where('status', 'active')->sum('total'),
            'totalRecurringInvoices' => $recurringInvoices->count(),
            'dataSource' => 'local_database'
        ]);
    }

    /**
     * Sync status of Kim's recurring invoices with Bexio
     * Only updates status of invoices that were created by Kim and sent to Bexio
     */
    private function syncKimRecurringInvoicesStatus($organization, string $accessToken): void
    {
        try {
            // Get Kim's invoices that have bexio_id (were sent to Bexio)
            $kimInvoicesInBexio = Invoice::forOrganization($organization->id)
                ->fromBexio()
                ->get();

            if ($kimInvoicesInBexio->isEmpty()) {
                Log::info("No Kim invoices found in Bexio for organization {$organization->id}");
                return;
            }

            $this->bexioService->setAccessToken($accessToken);

            foreach ($kimInvoicesInBexio as $kimInvoice) {
                try {
                    // Get current status from Bexio
                    $bexioInvoices = $this->bexioService->getInvoices();

                    if (is_array($bexioInvoices)) {
                        $bexioInvoice = collect($bexioInvoices)->firstWhere('id', $kimInvoice->bexio_id);

                        if ($bexioInvoice) {
                            $newStatus = $this->mapBexioStatusToLocal($bexioInvoice['kb_invoice_status_id'] ?? null);

                            if ($kimInvoice->status !== $newStatus) {
                                $kimInvoice->update([
                                    'status' => $newStatus,
                                    'last_synced_at' => now()
                                ]);

                                Log::info("Updated Kim invoice {$kimInvoice->id} status from {$kimInvoice->status} to {$newStatus}");
                            }
                        }
                    }
                } catch (\Exception $e) {
                    Log::error("Failed to sync status for Kim invoice {$kimInvoice->id}: " . $e->getMessage());
                }
            }

        } catch (\Exception $e) {
            Log::error("Failed to sync Kim recurring invoices status: " . $e->getMessage());
        }
    }

    /**
     * Map Bexio invoice status to local status
     */
    private function mapBexioStatusToLocal(?int $bexioStatusId): string
    {
        return match($bexioStatusId) {
            1 => 'draft',        // Draft
            2 => 'sent',         // Pending/Sent
            3 => 'sent',         // Sent
            4 => 'paid',         // Paid
            5 => 'cancelled',    // Cancelled
            6 => 'sent',         // Overdue (treat as sent)
            default => 'draft'
        };
    }
}
