<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use GuzzleHttp\Client;
use GuzzleHttp\Exception\RequestException;

class BexioApiController extends Controller
{
    private function makeApiRequest($endpoint)
    {
        // Check if we should use Personal Access Token
        if (config('bexio.use_personal_token') && config('bexio.personal_access_token')) {
            $accessToken = config('bexio.personal_access_token');
            Log::info("Using Personal Access Token for {$endpoint}");
        } else {
            // Use OAuth token from authenticated user
            $user = Auth::user();

            if (!$user || !$user->access_token) {
                return response()->json(['error' => 'No access token available'], 401);
            }

            $accessToken = $user->access_token;
            Log::info("Using OAuth token for user {$user->id} for {$endpoint}");
        }

        $client = new Client();

        try {
            $response = $client->get(config('bexio.api_base_url') . '/' . $endpoint, [
                'headers' => [
                    'Authorization' => 'Bearer ' . $accessToken,
                    'Accept' => 'application/json',
                    'Content-Type' => 'application/json'
                ]
            ]);

            return json_decode($response->getBody()->getContents(), true);

        } catch (RequestException $e) {
            $statusCode = $e->getResponse() ? $e->getResponse()->getStatusCode() : 'N/A';
            $responseBody = $e->getResponse() ? $e->getResponse()->getBody()->getContents() : 'N/A';

            Log::error("Bexio API error for {$endpoint}: {$statusCode} - {$responseBody}");

            return response()->json([
                'error' => 'API request failed',
                'message' => 'Failed to fetch data from Bexio'
            ], 500);
        }
    }

    public function getCustomers()
    {
        // If using Personal Token, skip user auth check
        if (config('bexio.use_personal_token') && config('bexio.personal_access_token')) {
            $data = $this->makeApiRequest('2.0/contact');
        } else {
            $user = Auth::user();

            if (!$user || !$user->access_token) {
                return response()->json(['error' => 'No access token available'], 401);
            }

            $data = $this->makeApiRequest('2.0/contact');
        }

        if (is_array($data)) {
            $userId = Auth::check() ? Auth::user()->id : 'personal-token';
            Log::info("Loaded " . count($data) . " customers from Bexio for user {$userId}");
            return response()->json(array_values($data));
        }

        // If API request failed, return empty array
        $userId = Auth::check() ? Auth::user()->id : 'personal-token';
        Log::warning("Bexio API failed, returning empty customers array for user {$userId}");
        return response()->json([]);
    }

    public function getUnits()
    {
        // If using Personal Token, skip user auth check
        if (config('bexio.use_personal_token') && config('bexio.personal_access_token')) {
            $data = $this->makeApiRequest('2.0/unit');
        } else {
            $user = Auth::user();

            if (!$user || !$user->access_token) {
                return response()->json(['error' => 'No access token available'], 401);
            }

            $data = $this->makeApiRequest('2.0/unit');
        }

        if (is_array($data)) {
            $userId = Auth::check() ? Auth::user()->id : 'personal-token';
            Log::info("Loaded " . count($data) . " units from Bexio for user {$userId}");
            return response()->json($data);
        }

        // If API request failed, return mock data as fallback
        $userId = Auth::check() ? Auth::user()->id : 'personal-token';
        Log::warning("Bexio API failed, returning mock units for user {$userId}");
        return response()->json([
            ['id' => 1, 'name' => 'Pieces'],
            ['id' => 2, 'name' => 'Hours'],
            ['id' => 3, 'name' => 'Days'],
            ['id' => 4, 'name' => 'Months'],
            ['id' => 5, 'name' => 'Years']
        ]);
    }

    public function getTaxes()
    {
        // If using Personal Token, skip user auth check
        if (config('bexio.use_personal_token') && config('bexio.personal_access_token')) {
            $data = $this->makeApiRequest('3.0/taxes');
        } else {
            $user = Auth::user();

            if (!$user || !$user->access_token) {
                return response()->json(['error' => 'No access token available'], 401);
            }

            $data = $this->makeApiRequest('3.0/taxes');
        }

        if (is_array($data)) {
            $userId = Auth::check() ? Auth::user()->id : 'personal-token';
            Log::info("Loaded " . count($data) . " taxes from Bexio for user {$userId}");
            return response()->json($data);
        }

        // If API request failed, return mock data as fallback
        $userId = Auth::check() ? Auth::user()->id : 'personal-token';
        Log::warning("Bexio API failed, returning mock taxes for user {$userId}");
        return response()->json([
            ['id' => 1, 'display_name' => '7.7% VAT', 'percentage' => 7.7],
            ['id' => 2, 'display_name' => '2.5% VAT', 'percentage' => 2.5],
            ['id' => 3, 'display_name' => '0% VAT', 'percentage' => 0],
            ['id' => 4, 'display_name' => '3.7% VAT', 'percentage' => 3.7]
        ]);
    }

    public function createCustomer(Request $request)
    {
        // Validate request data
        $request->validate([
            'customer.name' => 'required|string|max:255',
            'customer.email' => 'required|email|max:255',
            'customer.type' => 'nullable|integer|in:1,2', // 1=Customer, 2=Supplier
            'customer.address' => 'nullable|string',
            'customer.city' => 'nullable|string',
            'customer.postcode' => 'nullable|string',
            'customer.phone' => 'nullable|string',
            'customer.country_id' => 'nullable|integer'
        ]);

        // Use Personal Access Token for POST operations (required by Bexio)
        $personalToken = env('BEXIO_PERSONAL_ACCESS_TOKEN');

        if (!$personalToken) {
            return response()->json([
                'error' => 'Personal access token not configured',
                'message' => 'BEXIO_PERSONAL_ACCESS_TOKEN is required for creating contacts'
            ], 500);
        }

        $customerData = $request->input('customer');

        // Prepare data for Bexio API according to their specification
        $bexioData = [
            'contact_type_id' => $customerData['type'] ?? 1, // 1 = Customer, 2 = Supplier
            'name_1' => $customerData['name'],
            'salutation_id' => $customerData['salutation_id'] ?? 2, // Default salutation
            'address' => $customerData['address'] ?? '',
            'street_name' => $customerData['street_name'] ?? '',
            'house_number' => $customerData['house_number'] ?? '',
            'address_addition' => $customerData['address_addition'] ?? '',
            'postcode' => $customerData['postcode'] ?? '',
            'city' => $customerData['city'] ?? '',
            'country_id' => $customerData['country_id'] ?? 1, // Default to Switzerland
            'mail' => $customerData['email'],
            'mail_second' => $customerData['email_second'] ?? '',
            'phone_fixed' => $customerData['phone'] ?? '',
            'phone_fixed_second' => '',
            'phone_mobile' => $customerData['mobile'] ?? '',
            'fax' => '',
            'url' => $customerData['website'] ?? '',
            'skype_name' => '',
            'remarks' => $customerData['remarks'] ?? 'Created by Kim Rebill',
            'contact_group_ids' => $customerData['contact_group_ids'] ?? '1,2',
            'user_id' => 1,
            'owner_id' => 1
        ];

        $client = new Client();

        try {
            $response = $client->post(config('bexio.api_base_url') . '/2.0/contact', [
                'headers' => [
                    'Authorization' => 'Bearer ' . $personalToken,
                    'Accept' => 'application/json',
                    'Content-Type' => 'application/json'
                ],
                'json' => $bexioData,
                'timeout' => 30
            ]);

            $createdCustomer = json_decode($response->getBody()->getContents(), true);

            Log::info("Successfully created customer in Bexio", [
                'customer_id' => $createdCustomer['id'] ?? 'unknown',
                'name' => $createdCustomer['name_1'] ?? 'unknown'
            ]);

            return response()->json([
                'success' => true,
                'data' => $createdCustomer,
                'message' => 'Customer created successfully'
            ]);

        } catch (RequestException $e) {
            $statusCode = $e->getResponse() ? $e->getResponse()->getStatusCode() : 'N/A';
            $responseBody = $e->getResponse() ? $e->getResponse()->getBody()->getContents() : 'N/A';

            Log::error("Bexio API error creating customer", [
                'status_code' => $statusCode,
                'response_body' => $responseBody,
                'request_data' => $bexioData
            ]);

            // Parse error response for better user feedback
            $errorMessage = 'Could not create customer in Bexio';
            if ($responseBody !== 'N/A') {
                $errorData = json_decode($responseBody, true);
                if (isset($errorData['message'])) {
                    $errorMessage = $errorData['message'];
                } elseif (isset($errorData['error_description'])) {
                    $errorMessage = $errorData['error_description'];
                }
            }

            return response()->json([
                'success' => false,
                'error' => 'Failed to create customer',
                'message' => $errorMessage,
                'status_code' => $statusCode
            ], $statusCode === 'N/A' ? 500 : (int)$statusCode);
        }
    }
}
