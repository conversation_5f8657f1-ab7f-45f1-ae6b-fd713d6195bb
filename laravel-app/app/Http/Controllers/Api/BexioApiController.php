<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use GuzzleHttp\Client;
use GuzzleHttp\Exception\RequestException;

class BexioApiController extends Controller
{
    /**
     * Make API request using Personal Access Token
     */
    private function makeApiRequest($endpoint, $method = 'GET', $data = null)
    {
        if (!config('bexio.personal_access_token')) {
            throw new \Exception('Personal Access Token not configured');
        }

        $client = new Client();

        $options = [
            'headers' => [
                'Authorization' => 'Bearer ' . config('bexio.personal_access_token'),
                'Accept' => 'application/json',
                'Content-Type' => 'application/json'
            ],
            'timeout' => 30
        ];

        if ($data && in_array($method, ['POST', 'PUT', 'PATCH'])) {
            $options['json'] = $data;
        }

        try {
            $response = $client->request($method, config('bexio.api_base_url') . '/' . $endpoint, $options);
            return json_decode($response->getBody()->getContents(), true);

        } catch (RequestException $e) {
            $statusCode = $e->getResponse() ? $e->getResponse()->getStatusCode() : 'N/A';
            $responseBody = $e->getResponse() ? $e->getResponse()->getBody()->getContents() : 'N/A';

            Log::error("Bexio API error for {$method} {$endpoint}: {$statusCode} - {$responseBody}");
            throw new \Exception("API request failed: {$statusCode} - {$responseBody}");
        }
    }

    /**
     * Get customers from Bexio
     */
    public function getCustomers()
    {
        try {
            $data = $this->makeApiRequest('2.0/contact');

            if (is_array($data)) {
                Log::info("Loaded " . count($data) . " customers from Bexio");
                return response()->json($data);
            }

            return response()->json([]);

        } catch (\Exception $e) {
            Log::error("Failed to get customers: " . $e->getMessage());
            return response()->json(['error' => 'Failed to fetch customers'], 500);
        }
    }

    /**
     * Get units from Bexio
     */
    public function getUnits()
    {
        try {
            $data = $this->makeApiRequest('2.0/unit');

            if (is_array($data)) {
                Log::info("Loaded " . count($data) . " units from Bexio");
                return response()->json($data);
            }

            // If no data, return empty array
            return response()->json([]);

        } catch (\Exception $e) {
            Log::error("Failed to get units: " . $e->getMessage());

            // Return mock data as fallback
            Log::warning("Returning mock units as fallback");
            return response()->json([
                ['id' => 1, 'name' => 'Stk'],
                ['id' => 2, 'name' => 'h'],
                ['id' => 3, 'name' => 'Days'],
                ['id' => 4, 'name' => 'Months'],
                ['id' => 5, 'name' => 'Years']
            ]);
        }
    }

    /**
     * Get taxes from Bexio
     */
    public function getTaxes()
    {
        try {
            $data = $this->makeApiRequest('3.0/taxes');

            if (is_array($data)) {
                Log::info("Loaded " . count($data) . " taxes from Bexio");
                return response()->json($data);
            }

            // If no data, return empty array
            return response()->json([]);

        } catch (\Exception $e) {
            Log::error("Failed to get taxes: " . $e->getMessage());

            // Return mock data as fallback
            Log::warning("Returning mock taxes as fallback");
            return response()->json([
                ['id' => 3, 'display_name' => 'UEX - Export/Exempt 0.00%', 'percentage' => 0],
                ['id' => 4, 'display_name' => 'ULA - Leistungen im Ausland 0.00%', 'percentage' => 0],
                ['id' => 1, 'display_name' => '7.7% VAT', 'percentage' => 7.7],
                ['id' => 2, 'display_name' => '2.5% VAT', 'percentage' => 2.5]
            ]);
        }
    }

    /**
     * Create customer in Bexio
     */
    public function createCustomer(Request $request)
    {
        $request->validate([
            'customer.name' => 'required|string|max:255',
            'customer.email' => 'required|email|max:255',
            'customer.type' => 'nullable|integer|in:1,2',
            'customer.address' => 'nullable|string',
            'customer.city' => 'nullable|string',
            'customer.postcode' => 'nullable|string',
            'customer.phone' => 'nullable|string',
            'customer.country_id' => 'nullable|integer'
        ]);

        $customerData = $request->input('customer');

        // Prepare data for Bexio API
        $bexioData = [
            'contact_type_id' => $customerData['type'] ?? 2, // 2 = Individual person
            'name_1' => $customerData['name'],
            'salutation_id' => 1, // Default salutation
            'address' => $customerData['address'] ?? '',
            'postcode' => $customerData['postcode'] ?? '',
            'city' => $customerData['city'] ?? '',
            'country_id' => $customerData['country_id'] ?? 1, // Switzerland
            'mail' => $customerData['email'],
            'phone_fixed' => $customerData['phone'] ?? '',
            'remarks' => 'Created by Kim Rebill - ' . now()->format('Y-m-d H:i:s'),
            'contact_group_ids' => '1',
            'user_id' => 1,
            'owner_id' => 1
        ];

        try {
            $createdCustomer = $this->makeApiRequest('2.0/contact', 'POST', $bexioData);

            Log::info("Successfully created customer in Bexio", [
                'customer_id' => $createdCustomer['id'] ?? 'unknown',
                'name' => $createdCustomer['name_1'] ?? 'unknown'
            ]);

            return response()->json([
                'success' => true,
                'data' => $createdCustomer,
                'message' => 'Customer created successfully'
            ]);

        } catch (\Exception $e) {
            Log::error("Failed to create customer: " . $e->getMessage());

            return response()->json([
                'success' => false,
                'error' => 'Failed to create customer',
                'message' => $e->getMessage()
            ], 500);
        }
    }





    /**
     * Create invoice in Bexio
     */
    public function createInvoice(Request $request)
    {
        $request->validate([
            'invoice_id' => 'required|exists:invoices,id',
        ]);

        $invoice = \App\Models\Invoice::with('recurringTemplate')->findOrFail($request->invoice_id);

        // Ensure user can only create invoices for their own invoices
        if ($invoice->user_id !== Auth::id()) {
            return response()->json(['error' => 'Unauthorized'], 403);
        }

        // Check if invoice already has bexio_id
        if ($invoice->bexio_id) {
            return response()->json(['error' => 'Invoice already exists in Bexio'], 400);
        }

        try {
            // Get contact ID from contact_info JSON
            $contactInfo = is_string($invoice->contact_info) ? json_decode($invoice->contact_info, true) : $invoice->contact_info;
            $contactId = $contactInfo['contact_id'] ?? $contactInfo['id'] ?? null;

            if (!$contactId) {
                return response()->json(['error' => 'No contact ID found in invoice contact_info'], 400);
            }

            // Prepare invoice data for Bexio according to official API format
            $bexioInvoiceData = [
                'contact_id' => (int) $contactId,
                'user_id' => 1,
                'currency_id' => 1, // CHF
                'mwst_type' => (int) $invoice->tax_status,
                'mwst_is_net' => true,
                'show_position_taxes' => false,
                'is_valid_from' => now()->format('Y-m-d'),
                'title' => $invoice->title, // Use title instead of header
                'reference' => $invoice->document_nr, // Use reference for document number
                'positions' => collect(is_string($invoice->items) ? json_decode($invoice->items, true) : $invoice->items)->map(function ($item) {
                    return [
                        'amount' => (string) ($item['quantity'] ?? 1),
                        'unit_id' => (int) ($item['unit_id'] ?? 1),
                        'text' => $item['name'] ?? 'Item',
                        'unit_price' => (string) ($item['unit_price'] ?? 0),
                        'tax_id' => (int) ($item['tax_id'] ?? 1),
                        'type' => 'KbPositionCustom'
                    ];
                })->toArray()
            ];

            // Log the data being sent to Bexio for debugging
            Log::info("Creating invoice in Bexio", [
                'invoice_id' => $invoice->id,
                'bexio_data' => $bexioInvoiceData
            ]);

            // Create invoice in Bexio
            $response = $this->makeApiRequest('2.0/kb_invoice', 'POST', $bexioInvoiceData);

            if (isset($response['id'])) {
                // Update invoice with Bexio ID
                $invoice->update([
                    'bexio_id' => $response['id'],
                    'bexio_created_at' => now()
                ]);

                Log::info("Invoice {$invoice->id} created in Bexio with ID {$response['id']} for user " . Auth::id());

                return response()->json([
                    'success' => true,
                    'bexio_id' => $response['id'],
                    'message' => 'Invoice created successfully in Bexio'
                ]);
            } else {
                Log::error("Failed to create invoice in Bexio", ['response' => $response]);
                return response()->json(['error' => 'Failed to create invoice in Bexio'], 500);
            }

        } catch (\Exception $e) {
            Log::error("Error creating invoice in Bexio: " . $e->getMessage());
            return response()->json(['error' => 'Failed to create invoice: ' . $e->getMessage()], 500);
        }
    }

    /**
     * Send invoice email with template
     */
    public function sendInvoiceEmail(Request $request)
    {
        $request->validate([
            'invoice_id' => 'required|exists:invoices,id',
            'recipient_email' => 'required|email',
        ]);

        $invoice = \App\Models\Invoice::findOrFail($request->invoice_id);

        // Ensure user can only send emails for their own invoices
        if ($invoice->user_id !== Auth::id()) {
            return response()->json(['error' => 'Unauthorized'], 403);
        }

        // Check if invoice has bexio_id
        if (!$invoice->bexio_id) {
            return response()->json(['error' => 'Invoice must be created in Bexio first'], 400);
        }

        try {
            $user = Auth::user();

            // Get user's email template or fallback to base template
            $template = \App\Helpers\EmailTemplateHelper::getUserTemplate($user);

            // Prepare template data
            $contactInfo = json_decode($invoice->contact_info, true);
            $templateData = [
                'total' => number_format($invoice->total, 2),
                'date' => now()->format('M j, Y'),
                'valid_until' => now()->addDays(30)->format('M j, Y'),
                'document_number' => $invoice->document_nr,
                'title' => $invoice->title,
                'currency' => 'CHF',
                'name' => $contactInfo['name'] ?? 'Customer',
                'name_1' => $contactInfo['name'] ?? 'Customer',
                'name_2' => '',
                'user' => $user->name,
                'user_email' => $user->email,
                'company_name' => $user->organization->name ?? 'Your Company',
                'company_phone' => $user->organization->phone ?? '',
                'website' => $user->organization->website ?? '',
                'network_link' => '[Network Link]', // Bexio will replace this
                'project' => $invoice->title
            ];

            // Replace template variables with actual values
            $subject = \App\Helpers\EmailTemplateHelper::replaceVariables($template['subject'], $templateData);
            $body = \App\Helpers\EmailTemplateHelper::replaceVariables($template['body'], $templateData);

            // Send email via Bexio using template
            $emailData = [
                'recipient_email' => $request->recipient_email,
                'subject' => $subject,
                'message' => $body,
                'mark_as_open' => true,
                'attach_pdf' => true
            ];

            // Send email via Bexio API
            $response = $this->makeApiRequest("2.0/kb_invoice/{$invoice->bexio_id}/send", 'POST', $emailData);

            // Bexio send API might return different response formats
            if (!isset($response['error']) || $response['error'] === false) {
                Log::info("Invoice email sent for invoice {$invoice->id} to {$request->recipient_email} by user " . Auth::id());

                return response()->json([
                    'success' => true,
                    'message' => 'Invoice email sent successfully'
                ]);
            } else {
                Log::error("Failed to send invoice email", ['response' => $response]);
                return response()->json(['error' => 'Failed to send invoice email'], 500);
            }

        } catch (\Exception $e) {
            Log::error("Error sending invoice email: " . $e->getMessage());
            return response()->json(['error' => 'Failed to send email: ' . $e->getMessage()], 500);
        }
    }
}
