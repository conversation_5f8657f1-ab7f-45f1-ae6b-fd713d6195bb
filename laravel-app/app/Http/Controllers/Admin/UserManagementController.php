<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Models\Organization;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Auth;

class UserManagementController extends Controller
{
    /**
     * Display admin dashboard with regular user statistics (non-admin users)
     */
    public function index()
    {
        $stats = [
            'total_users' => User::where('is_admin', false)->count(),
            'active_users' => User::where('is_admin', false)->where('is_active', true)->count(),
            'trial_users' => User::where('is_admin', false)->where('subscription_status', 'trial')->count(),
            'expired_trials' => User::where('is_admin', false)->expiredTrials()->count(),
            'suspended_users' => User::where('is_admin', false)->where('is_active', false)->count(),
        ];

        $recentUsers = User::with('organization')
                          ->where('is_admin', false)
                          ->latest()
                          ->limit(10)
                          ->get();

        $expiredTrials = User::where('is_admin', false)
                            ->expiredTrials()
                            ->with('organization')
                            ->limit(5)
                            ->get();

        return view('admin.dashboard', compact('stats', 'recentUsers', 'expiredTrials'));
    }

    /**
     * Display user management page (admin users only)
     */
    public function users(Request $request)
    {
        $query = User::with('organization')->where('is_admin', true);

        // Apply filters for admin users
        if ($request->filled('status')) {
            switch ($request->status) {
                case 'active':
                    $query->where('is_active', true);
                    break;
                case 'suspended':
                    $query->where('is_active', false);
                    break;
            }
        }

        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('email', 'like', "%{$search}%");
            });
        }

        $users = $query->orderBy('created_at', 'desc')->paginate(20);

        return view('admin.users.index', compact('users'));
    }

    /**
     * Show user details
     */
    public function show(User $user)
    {
        $user->load(['organization', 'invoices' => function($query) {
            $query->latest()->limit(10);
        }]);

        $userStats = [
            'total_invoices' => $user->invoices()->count(),
            'recurring_templates' => $user->invoices()->recurringTemplates()->count(),
            'generated_invoices' => $user->invoices()->generatedFromRecurring()->count(),
            'last_login' => $user->last_login_at?->diffForHumans(),
            'trial_remaining' => $user->getRemainingTrialDays(),
        ];

        return view('admin.users.show', compact('user', 'userStats'));
    }

    /**
     * Show create user form
     */
    public function create()
    {
        $organizations = Organization::all();
        return view('admin.users.create', compact('organizations'));
    }

    /**
     * Store new user
     */
    public function store(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|string|email|max:255|unique:users',
            'password' => 'required|string|min:8|confirmed',
            'organization_id' => 'nullable|exists:organizations,id',
            'is_admin' => 'boolean',
            'subscription_status' => 'required|in:trial,active,suspended',
            'trial_days' => 'nullable|integer|min:1|max:365',
        ]);

        $userData = [
            'name' => $request->name,
            'email' => $request->email,
            'password' => Hash::make($request->password),
            'organization_id' => $request->organization_id,
            'is_admin' => $request->boolean('is_admin'),
            'is_active' => true,
            'subscription_status' => $request->subscription_status,
        ];

        // Set trial end date if trial user
        if ($request->subscription_status === 'trial' && $request->trial_days) {
            $userData['trial_ends_at'] = now()->addDays($request->trial_days)->toDateString();
        }

        $user = User::create($userData);

        Log::info("Admin created new user", [
            'admin_id' => Auth::id(),
            'user_id' => $user->id,
            'user_email' => $user->email
        ]);

        return redirect()->route('admin.users.show', $user)
                        ->with('success', 'User created successfully');
    }

    /**
     * Update user status (activate/suspend)
     */
    public function updateStatus(Request $request, User $user)
    {
        $request->validate([
            'action' => 'required|in:activate,suspend,extend_trial,activate_subscription'
        ]);

        switch ($request->action) {
            case 'activate':
                $user->reactivateUser();
                $message = 'User activated successfully';
                break;

            case 'suspend':
                $user->suspendUser();
                $message = 'User suspended successfully';
                break;

            case 'extend_trial':
                $request->validate(['days' => 'required|integer|min:1|max:365']);
                $user->extendTrial($request->days);
                $message = "Trial extended by {$request->days} days";
                break;

            case 'activate_subscription':
                $user->activateSubscription();
                $message = 'User subscription activated';
                break;
        }

        Log::info("Admin updated user status", [
            'admin_id' => Auth::id(),
            'user_id' => $user->id,
            'action' => $request->action,
            'details' => $request->only(['days'])
        ]);

        return redirect()->back()->with('success', $message);
    }

    /**
     * Delete user (soft delete or hard delete)
     */
    public function destroy(User $user)
    {
        if ($user->isAdmin() && User::admins()->count() <= 1) {
            return redirect()->back()->with('error', 'Cannot delete the last admin user');
        }

        $userName = $user->name;
        $userEmail = $user->email;

        // Hard delete for now (can be changed to soft delete)
        $user->delete();

        Log::warning("Admin deleted user", [
            'admin_id' => Auth::id(),
            'deleted_user_name' => $userName,
            'deleted_user_email' => $userEmail
        ]);

        return redirect()->route('admin.users.index')
                        ->with('success', "User {$userName} deleted successfully");
    }

    /**
     * Bulk actions for multiple users
     */
    public function bulkAction(Request $request)
    {
        $request->validate([
            'action' => 'required|in:activate,suspend,delete',
            'user_ids' => 'required|array',
            'user_ids.*' => 'exists:users,id'
        ]);

        $users = User::whereIn('id', $request->user_ids)->get();
        $processed = 0;

        foreach ($users as $user) {
            // Skip if trying to delete/suspend the last admin
            if (($request->action === 'delete' || $request->action === 'suspend') &&
                $user->isAdmin() && User::admins()->count() <= 1) {
                continue;
            }

            switch ($request->action) {
                case 'activate':
                    $user->reactivateUser();
                    $processed++;
                    break;
                case 'suspend':
                    $user->suspendUser();
                    $processed++;
                    break;
                case 'delete':
                    $user->delete();
                    $processed++;
                    break;
            }
        }

        Log::info("Admin performed bulk action", [
            'admin_id' => Auth::id(),
            'action' => $request->action,
            'user_count' => $processed
        ]);

        return redirect()->back()
                        ->with('success', "Bulk action completed. {$processed} users processed.");
    }
}
