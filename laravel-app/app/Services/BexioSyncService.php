<?php

namespace App\Services;

use App\Models\Invoice;
use App\Models\Organization;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;

class BexioSyncService
{
    protected BexioServiceInterface $bexioService;

    public function __construct(BexioServiceInterface $bexioService)
    {
        $this->bexioService = $bexioService;
    }

    /**
     * Sync all invoices for an organization with Bexio API
     */
    public function syncInvoicesForOrganization(Organization $organization, string $accessToken): array
    {
        $this->bexioService->setAccessToken($accessToken);

        try {
            // Get invoices from Bexio API
            $bexioInvoices = $this->bexioService->getInvoices();
            
            if (!is_array($bexioInvoices)) {
                throw new \Exception('Invalid response from Bexio API');
            }

            $syncStats = [
                'total_bexio_invoices' => count($bexioInvoices),
                'created' => 0,
                'updated' => 0,
                'errors' => 0
            ];

            DB::beginTransaction();

            foreach ($bexioInvoices as $bexioInvoice) {
                try {
                    $result = $this->syncSingleInvoice($bexioInvoice, $organization->id);
                    $syncStats[$result]++;
                } catch (\Exception $e) {
                    $syncStats['errors']++;
                    Log::error("Failed to sync invoice {$bexioInvoice['id']}: " . $e->getMessage());
                }
            }

            DB::commit();

            Log::info("Sync completed for organization {$organization->id}", $syncStats);
            return $syncStats;

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error("Sync failed for organization {$organization->id}: " . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Sync a single invoice with local database
     */
    protected function syncSingleInvoice(array $bexioInvoice, int $organizationId): string
    {
        $bexioId = $bexioInvoice['id'];
        
        // Find existing invoice
        $localInvoice = Invoice::where('bexio_id', $bexioId)
            ->where('organization_id', $organizationId)
            ->first();

        $invoiceData = $this->mapBexioInvoiceToLocal($bexioInvoice, $organizationId);

        if ($localInvoice) {
            // Update existing invoice
            $localInvoice->update($invoiceData);
            $localInvoice->markAsSynced();
            return 'updated';
        } else {
            // Create new invoice
            $invoiceData['last_synced_at'] = now();
            Invoice::create($invoiceData);
            return 'created';
        }
    }

    /**
     * Map Bexio invoice data to local invoice structure
     */
    protected function mapBexioInvoiceToLocal(array $bexioInvoice, int $organizationId): array
    {
        return [
            'organization_id' => $organizationId,
            'bexio_id' => $bexioInvoice['id'],
            'title' => $bexioInvoice['title'] ?? 'Untitled Invoice',
            'document_nr' => $bexioInvoice['document_nr'] ?? '',
            'contact_info' => $this->extractContactInfo($bexioInvoice),
            'total' => $bexioInvoice['total_gross'] ?? 0,
            'status' => $this->mapBexioStatusToLocal($bexioInvoice['kb_invoice_status_id'] ?? null),
            'is_recurring' => false, // Will be updated separately for recurring invoices
        ];
    }

    /**
     * Extract contact information from Bexio invoice
     */
    protected function extractContactInfo(array $bexioInvoice): array
    {
        return [
            'contact_id' => $bexioInvoice['contact_id'] ?? null,
            'name' => $bexioInvoice['contact_name'] ?? '',
            'address' => $bexioInvoice['contact_address'] ?? '',
            'email' => $bexioInvoice['contact_mail'] ?? '',
        ];
    }

    /**
     * Map Bexio invoice status to local status
     */
    protected function mapBexioStatusToLocal(?int $bexioStatusId): string
    {
        return match($bexioStatusId) {
            1 => 'draft',        // Draft
            2 => 'sent',         // Pending/Sent
            3 => 'sent',         // Sent
            4 => 'paid',         // Paid
            5 => 'cancelled',    // Cancelled
            6 => 'sent',         // Overdue (treat as sent)
            default => 'draft'
        };
    }

    /**
     * Sync recurring invoices for an organization
     */
    public function syncRecurringInvoicesForOrganization(Organization $organization, string $accessToken): array
    {
        $this->bexioService->setAccessToken($accessToken);

        try {
            $bexioRecurringInvoices = $this->bexioService->getRecurringInvoices();
            
            if (!is_array($bexioRecurringInvoices)) {
                return ['total' => 0, 'updated' => 0];
            }

            $updated = 0;

            foreach ($bexioRecurringInvoices as $recurringInvoice) {
                // Find invoices that match this recurring template
                $invoiceId = $recurringInvoice['kb_invoice_id'] ?? null;
                
                if ($invoiceId) {
                    $localInvoice = Invoice::where('bexio_id', $invoiceId)
                        ->where('organization_id', $organization->id)
                        ->first();

                    if ($localInvoice) {
                        $localInvoice->update([
                            'is_recurring' => true,
                            'recurring_settings' => [
                                'recurring_id' => $recurringInvoice['id'],
                                'interval' => $recurringInvoice['interval'] ?? null,
                                'next_date' => $recurringInvoice['next_date'] ?? null,
                            ]
                        ]);
                        $updated++;
                    }
                }
            }

            Log::info("Synced {$updated} recurring invoices for organization {$organization->id}");
            
            return [
                'total' => count($bexioRecurringInvoices),
                'updated' => $updated
            ];

        } catch (\Exception $e) {
            Log::error("Failed to sync recurring invoices for organization {$organization->id}: " . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Get sync statistics for an organization
     */
    public function getSyncStats(Organization $organization): array
    {
        $totalInvoices = Invoice::forOrganization($organization->id)->count();
        $bexioInvoices = Invoice::forOrganization($organization->id)->fromBexio()->count();
        $localOnlyInvoices = Invoice::forOrganization($organization->id)->localOnly()->count();
        $needsSyncCount = Invoice::forOrganization($organization->id)
            ->fromBexio()
            ->get()
            ->filter(fn($invoice) => $invoice->needsSync())
            ->count();

        return [
            'total_invoices' => $totalInvoices,
            'bexio_invoices' => $bexioInvoices,
            'local_only_invoices' => $localOnlyInvoices,
            'needs_sync' => $needsSyncCount,
            'last_sync' => Invoice::forOrganization($organization->id)
                ->fromBexio()
                ->whereNotNull('last_synced_at')
                ->max('last_synced_at')
        ];
    }
}
