<?php

namespace App\Services;

use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use GuzzleHttp\Client;
use GuzzleHttp\Exception\RequestException;

class BexioRealService implements BexioServiceInterface
{
    protected string $clientId;
    protected string $clientSecret;
    protected string $redirectUri;
    protected ?string $accessToken = null;

    public function __construct()
    {
        $this->clientId = config('bexio.client_id');
        $this->clientSecret = config('bexio.client_secret');
        $this->redirectUri = config('bexio.redirect_uri');
    }

    public function getAuthUrl(): string
    {
        $scopes = config('bexio.scopes', [
            'openid',
            'profile',
            'email',
            'offline_access',
            'kb_invoice_edit',
            'contact_edit',
            'company_profile'
        ]);

        return 'https://office.bexio.com/oauth/authorize?' . http_build_query([
            'client_id' => $this->clientId,
            'redirect_uri' => $this->redirectUri,
            'response_type' => 'code',
            'scope' => implode(' ', $scopes)
        ]);
    }

    public function handleCallback(string $code): array
    {
        $response = Http::asForm()->post('https://office.bexio.com/oauth/token', [
            'client_id' => $this->clientId,
            'client_secret' => $this->clientSecret,
            'code' => $code,
            'grant_type' => 'authorization_code',
            'redirect_uri' => $this->redirectUri
        ]);

        $data = $response->json();
        $this->accessToken = $data['access_token'] ?? null;

        return $data;
    }

    public function getAccessToken(): ?string
    {
        return $this->accessToken;
    }

    public function setAccessToken(string $token): void
    {
        $this->accessToken = $token;
    }

    public function refreshToken(): array
    {
        // Implementation for token refresh
        return [];
    }

    public function getUserData(): array
    {
        return $this->bexioRequest('GET', 'v1/users/me');
    }

    public function getCompanyProfile(): array
    {
        return $this->bexioRequest('GET', '3.0/company_profile');
    }

    public function getInvoices(): array
    {
        return $this->bexioRequest('GET', '3.0/kb_invoice');
    }

    public function getContacts(): array
    {
        return $this->bexioRequest('GET', '3.0/contact');
    }

    public function getContact($id): array
    {
        return $this->bexioRequest('GET', "3.0/contact/{$id}");
    }

    public function getUnits(): array
    {
        return $this->bexioRequest('GET', '2.0/units');
    }

    public function getTaxes(): array
    {
        return $this->bexioRequest('GET', '2.0/tax');
    }

    public function getCurrencies(): array
    {
        return $this->bexioRequest('GET', '2.0/currency');
    }

    public function createCustomer(array $data): array
    {
        return $this->bexioRequest('POST', '3.0/contact', $data);
    }

    public function createInvoice(array $data): array
    {
        return $this->bexioRequest('POST', '3.0/kb_invoice', $data);
    }

    public function updateInvoice($id, array $data): array
    {
        return $this->bexioRequest('POST', "3.0/kb_invoice/{$id}", $data);
    }

    public function restartRecurringBilling($id): array
    {
        return $this->bexioRequest('POST', "3.0/recurring_bill/{$id}/restart");
    }

    public function deleteRecurringBilling($id): array
    {
        return $this->bexioRequest('DELETE', "3.0/recurring_bill/{$id}");
    }

    public function pauseRecurringBilling($id): array
    {
        return $this->bexioRequest('POST', "3.0/recurring_bill/{$id}/pause");
    }

    private function bexioRequest(string $method, string $path, ?array $data = null): array
    {
        if (!$this->accessToken) {
            return [];
        }

        $client = new Client();
        $url = config('bexio.api_base_url') . '/' . $path;

        try {
            $options = [
                'headers' => [
                    'Authorization' => 'Bearer ' . $this->accessToken,
                    'Accept' => 'application/json',
                    'Content-Type' => 'application/json'
                ]
            ];

            if ($data && in_array(strtoupper($method), ['POST', 'PUT', 'PATCH'])) {
                $options['json'] = $data;
            }

            $response = $client->request($method, $url, $options);

            return json_decode($response->getBody()->getContents(), true) ?? [];

        } catch (RequestException $e) {
            $statusCode = $e->getResponse() ? $e->getResponse()->getStatusCode() : 'N/A';
            $responseBody = $e->getResponse() ? $e->getResponse()->getBody()->getContents() : 'N/A';

            Log::error("Bexio API error: {$statusCode} - {$responseBody}");
            return [];
        } catch (\Exception $e) {
            Log::error("Bexio API exception: " . $e->getMessage());
            return [];
        }
    }

    public function getRecurringInvoices(): array
    {
        // Get recurring billing templates from Bexio API
        return $this->bexioRequest('GET', '3.0/recurring_bill');
    }

    public function getDrafts(): array
    {
        // Get draft invoices from Bexio API
        // Filter invoices with status 1 (draft)
        $allInvoices = $this->bexioRequest('GET', '3.0/kb_invoice');

        if (!is_array($allInvoices)) {
            return [];
        }

        // Filter for draft invoices (status_id = 1)
        return array_filter($allInvoices, function($invoice) {
            return isset($invoice['kb_invoice_status_id']) && $invoice['kb_invoice_status_id'] == 1;
        });
    }

    public function getInvoicesByStatus(int $statusId): array
    {
        // Get invoices filtered by status
        $allInvoices = $this->bexioRequest('GET', '3.0/kb_invoice');

        if (!is_array($allInvoices)) {
            return [];
        }

        return array_filter($allInvoices, function($invoice) use ($statusId) {
            return isset($invoice['kb_invoice_status_id']) && $invoice['kb_invoice_status_id'] == $statusId;
        });
    }

    public function getUnpaidInvoices(): array
    {
        // Get invoices that are not paid (status != 4) and not cancelled (status != 5)
        $allInvoices = $this->bexioRequest('GET', '3.0/kb_invoice');

        if (!is_array($allInvoices)) {
            return [];
        }

        return array_filter($allInvoices, function($invoice) {
            $status = $invoice['kb_invoice_status_id'] ?? 0;
            return $status != 4 && $status != 5; // Not paid and not cancelled
        });
    }

    public function getPaidInvoices(): array
    {
        // Get paid invoices (status = 4)
        return $this->getInvoicesByStatus(4);
    }
}
