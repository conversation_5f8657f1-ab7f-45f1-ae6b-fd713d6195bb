<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\Invoice;
use App\Models\User;
use App\Http\Controllers\Api\BexioApiController;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;


class ProcessRecurringInvoices extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'rebill:process-recurring
                            {--dry-run : Show what would be processed without actually creating invoices}
                            {--user= : Process only for specific user ID}
                            {--force : Force process even if not due date}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Process recurring invoices and send them via Bexio API';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('🔄 Starting Kim Rebill Recurring Invoice Processing');
        $this->info('================================================');

        $dryRun = $this->option('dry-run');
        $userId = $this->option('user');
        $force = $this->option('force');

        if ($dryRun) {
            $this->warn('🧪 DRY RUN MODE - No invoices will be actually created');
        }

        // Get recurring templates that are due for processing
        $query = Invoice::where('is_recurring', true)
                        ->where('created_from_recurring', false)
                        ->where('status', 'active');

        if ($userId) {
            $query->where('user_id', $userId);
            $this->info("🎯 Processing only for user ID: {$userId}");
        }

        if (!$force) {
            // Only process templates that are due
            $query->where(function($q) {
                $q->whereNull('next_run_date')
                  ->orWhere('next_run_date', '<=', now()->toDateString());
            });
        }

        $recurringInvoices = $query->get();

        if ($recurringInvoices->isEmpty()) {
            $this->info('✅ No recurring invoices found to process');
            return 0;
        }

        $this->info("📋 Found {$recurringInvoices->count()} recurring invoices to process");
        $this->newLine();

        $processed = 0;
        $failed = 0;

        foreach ($recurringInvoices as $invoice) {
            $this->processRecurringInvoice($invoice, $dryRun, $processed, $failed);
        }

        $this->newLine();
        $this->info('📊 Processing Summary:');
        $this->info("✅ Successfully processed: {$processed}");
        $this->info("❌ Failed: {$failed}");
        $this->info("📈 Total: " . ($processed + $failed));

        return 0;
    }

    /**
     * Process a single recurring invoice
     */
    private function processRecurringInvoice(Invoice $templateInvoice, bool $dryRun, int &$processed, int &$failed)
    {
        $this->info("🔄 Processing: {$templateInvoice->title} (ID: {$templateInvoice->id})");

        try {
            // Authenticate as the invoice owner
            $user = User::find($templateInvoice->user_id);
            if (!$user) {
                throw new \Exception("User not found for invoice {$templateInvoice->id}");
            }

            Auth::login($user);
            $this->line("   👤 Authenticated as: {$user->name}");

            // Create new invoice instance based on template
            $newInvoiceData = [
                'user_id' => $templateInvoice->user_id,
                'organization_id' => $templateInvoice->organization_id,
                'title' => $templateInvoice->title,
                'document_nr' => $this->generateDocumentNumber($templateInvoice),
                'contact_info' => $templateInvoice->contact_info,
                'total' => $templateInvoice->total,
                'status' => 'active',
                'is_recurring' => false, // This is the actual invoice, not template
                'items' => $templateInvoice->items,
                'tax_status' => $templateInvoice->tax_status,
                'recurring_template_id' => $templateInvoice->id,
                'created_from_recurring' => true
            ];

            if ($dryRun) {
                $this->line("   🧪 DRY RUN: Would create invoice with document nr: {$newInvoiceData['document_nr']}");
                $processed++;
                return;
            }

            // Create new invoice in database
            $newInvoice = Invoice::create($newInvoiceData);
            $this->line("   ✅ Created invoice in database (ID: {$newInvoice->id})");

            // Step 1: Create invoice in Bexio
            $this->line("   📡 Creating invoice in Bexio...");
            $bexioController = new BexioApiController();

            $createRequest = new Request(['invoice_id' => $newInvoice->id]);
            $createResponse = $bexioController->createInvoice($createRequest);
            $createResult = json_decode($createResponse->getContent(), true);

            if (!isset($createResult['success']) || !$createResult['success']) {
                throw new \Exception("Failed to create invoice in Bexio: " . ($createResult['error'] ?? 'Unknown error'));
            }

            $bexioId = $createResult['bexio_id'];
            $this->line("   ✅ Created in Bexio (ID: {$bexioId})");

            // Step 2: Send invoice email
            $contactInfo = json_decode($newInvoice->contact_info, true);
            $recipientEmail = $contactInfo['email'] ?? null;

            if ($recipientEmail) {
                $this->line("   📧 Sending email to: {$recipientEmail}");

                $emailRequest = new Request([
                    'invoice_id' => $newInvoice->id,
                    'recipient_email' => $recipientEmail
                ]);

                $emailResponse = $bexioController->sendInvoiceEmail($emailRequest);
                $emailResult = json_decode($emailResponse->getContent(), true);

                if (isset($emailResult['success']) && $emailResult['success']) {
                    $this->line("   ✅ Email sent successfully");
                } else {
                    $this->warn("   ⚠️  Email sending failed: " . ($emailResult['error'] ?? 'Unknown error'));
                    // Don't fail the whole process if email fails
                }
            } else {
                $this->warn("   ⚠️  No email address found in contact info");
            }

            // Update template's last processed timestamp and next run date
            $templateInvoice->markAsProcessed();

            $this->info("   🎉 Successfully processed recurring invoice!");
            $processed++;

        } catch (\Exception $e) {
            $this->error("   ❌ Failed to process invoice: " . $e->getMessage());
            Log::error("Recurring invoice processing failed", [
                'invoice_id' => $templateInvoice->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            $failed++;
        }

        $this->newLine();
    }

    /**
     * Generate document number for new invoice
     */
    private function generateDocumentNumber(Invoice $templateInvoice): string
    {
        $prefix = 'REC';
        $date = now()->format('Ymd');
        $templateId = str_pad($templateInvoice->id, 3, '0', STR_PAD_LEFT);
        $generatedCount = Invoice::where('recurring_template_id', $templateInvoice->id)->count();
        $sequence = str_pad($generatedCount + 1, 3, '0', STR_PAD_LEFT);

        return "{$prefix}-{$date}-T{$templateId}-{$sequence}";
    }
}
