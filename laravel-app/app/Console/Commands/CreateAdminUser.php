<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\User;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Validator;

class CreateAdminUser extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'rebill:create-admin
                            {--email= : Admin email address}
                            {--password= : Admin password}
                            {--name= : Admin full name}
                            {--update : Update existing admin if email exists}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Create or update admin user with secure password hash';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('🔐 Kim Rebill Admin User Management');
        $this->info('=====================================');

        // Get input data
        $email = $this->option('email') ?: $this->ask('Admin email address', '<EMAIL>');
        $name = $this->option('name') ?: $this->ask('Admin full name', 'Super Admin');
        $password = $this->option('password') ?: $this->secret('Admin password');

        // Validate input
        $validator = Validator::make([
            'email' => $email,
            'name' => $name,
            'password' => $password,
        ], [
            'email' => 'required|email',
            'name' => 'required|string|min:2',
            'password' => 'required|string|min:8',
        ]);

        if ($validator->fails()) {
            $this->error('Validation failed:');
            foreach ($validator->errors()->all() as $error) {
                $this->error("  - {$error}");
            }
            return 1;
        }

        // Check if user exists
        $existingUser = User::where('email', $email)->first();

        if ($existingUser && !$this->option('update')) {
            $this->error("User with email {$email} already exists!");
            $this->info("Use --update flag to update existing user.");
            return 1;
        }

        try {
            if ($existingUser) {
                // Update existing user
                $existingUser->update([
                    'name' => $name,
                    'password' => Hash::make($password),
                    'is_admin' => true,
                    'is_active' => true,
                    'subscription_status' => 'active',
                ]);

                $this->info("✅ Admin user updated successfully!");
                $this->info("📧 Email: {$email}");
                $this->info("👤 Name: {$name}");
                $this->info("🔑 Password: [UPDATED - SECURELY HASHED]");

            } else {
                // Create new user
                $admin = User::create([
                    'name' => $name,
                    'email' => $email,
                    'password' => Hash::make($password),
                    'is_admin' => true,
                    'is_active' => true,
                    'subscription_status' => 'active',
                    'organization_id' => 1, // Default organization
                ]);

                $this->info("✅ Admin user created successfully!");
                $this->info("📧 Email: {$email}");
                $this->info("👤 Name: {$name}");
                $this->info("🆔 User ID: {$admin->id}");
                $this->info("🔑 Password: [SECURELY HASHED]");
            }

            // Test password hash
            $testUser = User::where('email', $email)->first();
            $isValid = Hash::check($password, $testUser->password);

            if ($isValid) {
                $this->info("🔐 Password hash verification: PASSED");
            } else {
                $this->error("🔐 Password hash verification: FAILED");
                return 1;
            }

            $this->newLine();
            $this->info("🌐 Admin login URL: " . url('/admin/login'));
            $this->info("🎯 You can now login with the credentials above.");

            return 0;

        } catch (\Exception $e) {
            $this->error("Failed to create/update admin user: " . $e->getMessage());
            return 1;
        }
    }
}
