@extends('layouts.app')

@section('title', 'Settings')

@section('content')
<div class="container">
    @if (session('success'))
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            {{ session('success') }}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    @endif

    @if (session('info'))
        <div class="alert alert-info alert-dismissible fade show" role="alert">
            {{ session('info') }}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    @endif

    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h6 class="text-muted mb-1">Account</h6>
                    <h1 class="display-5 fw-bold mb-0">Settings</h1>
                </div>
                <a href="{{ route('dashboard') }}" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-left me-1"></i>Back to Dashboard
                </a>
            </div>
        </div>
    </div>

    <!-- Settings Tabs -->
    <div class="row">
        <div class="col-12">
            <ul class="nav nav-tabs" id="settingsTabs" role="tablist">
                <li class="nav-item" role="presentation">
                    <button class="nav-link active" id="profile-tab" data-bs-toggle="tab" data-bs-target="#profile" type="button" role="tab">
                        <i class="fas fa-user me-1"></i>Profile
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="templates-tab" data-bs-toggle="tab" data-bs-target="#templates" type="button" role="tab">
                        <i class="fas fa-file-invoice me-1"></i>Templates
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="security-tab" data-bs-toggle="tab" data-bs-target="#security" type="button" role="tab">
                        <i class="fas fa-lock me-1"></i>Security
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="bexio-tab" data-bs-toggle="tab" data-bs-target="#bexio" type="button" role="tab">
                        <i class="fas fa-link me-1"></i>Bexio Integration
                    </button>
                </li>
                <li class="nav-item ms-auto" role="presentation">
                    <form method="POST" action="{{ route('logout') }}" class="d-inline">
                        @csrf
                        <button type="submit" class="nav-link text-danger border-0 bg-transparent">
                            <i class="fas fa-sign-out-alt me-1"></i>Logout
                        </button>
                    </form>
                </li>
            </ul>

            <div class="tab-content mt-4" id="settingsTabContent">
                <!-- Profile Tab -->
                <div class="tab-pane fade show active" id="profile" role="tabpanel">
                    <div class="card border-0 shadow-sm">
                        <div class="card-header bg-white">
                            <h5 class="mb-0 fw-semibold">Profile Information</h5>
                        </div>
                        <div class="card-body">
                            @if ($errors->updateProfile->any())
                                <div class="alert alert-danger">
                                    <ul class="mb-0">
                                        @foreach ($errors->updateProfile->all() as $error)
                                            <li>{{ $error }}</li>
                                        @endforeach
                                    </ul>
                                </div>
                            @endif

                            <form method="POST" action="{{ route('settings.profile.update') }}">
                                @csrf
                                @method('PUT')

                                <div class="row g-3">
                                    <div class="col-md-6">
                                        <label for="name" class="form-label">Full Name</label>
                                        <input type="text" class="form-control" id="name" name="name"
                                               value="{{ old('name', $user->name) }}" required>
                                    </div>
                                    <div class="col-md-6">
                                        <label for="email" class="form-label">Email Address</label>
                                        <input type="email" class="form-control" id="email" name="email"
                                               value="{{ old('email', $user->email) }}" required>
                                    </div>
                                </div>

                                <div class="mt-3">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-save me-1"></i>Save Changes
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>

                <!-- Templates Tab -->
                <div class="tab-pane fade" id="templates" role="tabpanel">
                    <div class="card border-0 shadow-sm">
                        <div class="card-header bg-white">
                            <h5 class="mb-0 fw-semibold">Email Templates</h5>
                        </div>
                        <div class="card-body">
                            @if ($errors->updateTemplates->any())
                                <div class="alert alert-danger">
                                    <ul class="mb-0">
                                        @foreach ($errors->updateTemplates->all() as $error)
                                            <li>{{ $error }}</li>
                                        @endforeach
                                    </ul>
                                </div>
                            @endif

                            <form method="POST" action="{{ route('settings.templates.update') }}">
                                @csrf
                                @method('PUT')

                                <div class="row g-3">
                                    <div class="col-md-4">
                                        <label for="template_language" class="form-label">Language</label>
                                        <select class="form-select" id="template_language" name="template_language">
                                            <option value="EN" {{ old('template_language', $user->template_language ?? 'EN') === 'EN' ? 'selected' : '' }}>English</option>
                                            <option value="DE" {{ old('template_language', $user->template_language ?? 'EN') === 'DE' ? 'selected' : '' }}>German</option>
                                            <option value="FR" {{ old('template_language', $user->template_language ?? 'EN') === 'FR' ? 'selected' : '' }}>French</option>
                                        </select>
                                        <div class="form-text">Select a template language</div>
                                    </div>
                                </div>

                                <div class="row g-3 mt-2">
                                    <div class="col-12">
                                        <label for="email_subject" class="form-label">Email Subject</label>
                                        <input type="text" class="form-control" id="email_subject" name="email_subject"
                                               value="{{ old('email_subject', $user->email_subject ?? 'RE: Invoice') }}"
                                               placeholder="RE: Invoice">
                                    </div>
                                    <div class="col-12">
                                        <label for="email_body" class="form-label">Email Body</label>
                                        <textarea class="form-control" id="email_body" name="email_body" rows="12"
                                                  placeholder="Enter your email template...">{{ old('email_body', $user->email_body ?? '') }}</textarea>
                                    </div>
                                </div>

                                <div class="mt-3">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-save me-1"></i>Save Changes
                                    </button>
                                </div>
                            </form>

                            <!-- Invoice Variables Alert -->
                            <div class="alert alert-info mt-4">
                                <h6 class="alert-heading">
                                    <i class="fas fa-info-circle me-1"></i>Invoice Variables
                                </h6>
                                <p class="mb-2">The following variables can be used to fill in dynamic customer/invoice information:</p>
                                <div class="row g-2">
                                    <div class="col-md-6">
                                        <small class="d-block"><strong>[Total]</strong> → Total amount</small>
                                        <small class="d-block"><strong>[Date]</strong> → Invoice date</small>
                                        <small class="d-block"><strong>[Valid Until]</strong> → Expiry date</small>
                                        <small class="d-block"><strong>[Document Number]</strong> → Invoice number</small>
                                        <small class="d-block"><strong>[Project]</strong> → Project name</small>
                                        <small class="d-block"><strong>[Title]</strong> → Invoice title</small>
                                        <small class="d-block"><strong>[Currency]</strong> → Currency code</small>
                                    </div>
                                    <div class="col-md-6">
                                        <small class="d-block"><strong>[Name 1]</strong> → Last name / Company</small>
                                        <small class="d-block"><strong>[Name]</strong> → Full name</small>
                                        <small class="d-block"><strong>[Name 2]</strong> → First name / Company</small>
                                        <small class="d-block"><strong>[User]</strong> → User name</small>
                                        <small class="d-block"><strong>[User Email]</strong> → User email</small>
                                        <small class="d-block"><strong>[Company Name]</strong> → Organization</small>
                                        <small class="d-block"><strong>[Company Phone Nr]</strong> → Phone number</small>
                                        <small class="d-block"><strong>[Website]</strong> → Website URL</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Security Tab -->
                <div class="tab-pane fade" id="security" role="tabpanel">
                    <div class="card border-0 shadow-sm">
                        <div class="card-header bg-white">
                            <h5 class="mb-0 fw-semibold">Change Password</h5>
                        </div>
                        <div class="card-body">
                            @if ($errors->updatePassword->any())
                                <div class="alert alert-danger">
                                    <ul class="mb-0">
                                        @foreach ($errors->updatePassword->all() as $error)
                                            <li>{{ $error }}</li>
                                        @endforeach
                                    </ul>
                                </div>
                            @endif

                            <form method="POST" action="{{ route('settings.password.update') }}">
                                @csrf
                                @method('PUT')

                                <div class="row g-3">
                                    <div class="col-md-6">
                                        <label for="current_password" class="form-label">Current Password</label>
                                        <input type="password" class="form-control" id="current_password" name="current_password" required>
                                    </div>
                                    <div class="col-md-6">
                                        <label for="new_password" class="form-label">New Password</label>
                                        <input type="password" class="form-control" id="new_password" name="new_password" required>
                                    </div>
                                    <div class="col-md-6">
                                        <label for="new_password_confirmation" class="form-label">Confirm New Password</label>
                                        <input type="password" class="form-control" id="new_password_confirmation" name="new_password_confirmation" required>
                                    </div>
                                </div>

                                <div class="mt-3">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-key me-1"></i>Update Password
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>

                <!-- Bexio Integration Tab -->
                <div class="tab-pane fade" id="bexio" role="tabpanel">
                    <div class="card border-0 shadow-sm">
                        <div class="card-header bg-white">
                            <h5 class="mb-0 fw-semibold">Bexio Integration</h5>
                        </div>
                        <div class="card-body">
                            <div class="row g-4">
                                <div class="col-12">
                                    <div class="d-flex align-items-center justify-content-between p-3 bg-light rounded">
                                        <div>
                                            <h6 class="mb-1">Connection Status</h6>
                                            <small class="text-muted">
                                                @if($user->bexio_access_token)
                                                    <span class="badge bg-success">Connected</span>
                                                    Connected as {{ $user->name }}
                                                @else
                                                    <span class="badge bg-warning">Not Connected</span>
                                                    Please connect your Bexio account
                                                @endif
                                            </small>
                                        </div>
                                        <div>
                                            @if($user->bexio_access_token)
                                                <form method="POST" action="{{ route('settings.bexio.reset') }}" class="d-inline">
                                                    @csrf
                                                    <button type="submit" class="btn btn-outline-danger btn-sm">
                                                        <i class="fas fa-unlink me-1"></i>Disconnect
                                                    </button>
                                                </form>
                                            @else
                                                <a href="{{ route('bexio.login') }}" class="btn btn-primary btn-sm">
                                                    <i class="fas fa-link me-1"></i>Connect Bexio
                                                </a>
                                            @endif
                                        </div>
                                    </div>
                                </div>

                                @if($user->bexio_access_token)
                                <div class="col-12">
                                    <div class="alert alert-info">
                                        <h6 class="alert-heading">
                                            <i class="fas fa-info-circle me-1"></i>Integration Details
                                        </h6>
                                        <p class="mb-2"><strong>Bexio ID:</strong> {{ $user->bexio_id }}</p>
                                        <p class="mb-2"><strong>Token Expires:</strong>
                                            @if($user->bexio_token_expires_at)
                                                {{ $user->bexio_token_expires_at->format('M j, Y g:i A') }}
                                            @else
                                                Never
                                            @endif
                                        </p>
                                        <hr>
                                        <p class="mb-0">Your Bexio account is connected and ready to sync invoices.</p>
                                    </div>
                                </div>
                                @endif
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
