@extends('layouts.app')

@section('title', 'Dashboard')

@section('content')
<div class="container-fluid">
    @if (session('status'))
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            {{ session('status') }}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    @endif

    <!-- Header Section -->
    <div class="row align-items-center mb-4">
        <div class="col-md-6">
            <div class="mb-2">
                <h6 class="text-muted mb-1">Welcome back, {{ Auth::user()?->name ?? 'User' }}</h6>
                <h1 class="display-5 fw-bold mb-0">Dashboard</h1>
            </div>
        </div>
        <div class="col-md-6">
            <div class="row g-3">
                <div class="col-6">
                    <div class="card bg-warning bg-opacity-10 border-warning border-opacity-25 h-100">
                        <div class="card-body p-3 text-center">
                            <h6 class="text-warning small mb-1">Unpaid Invoices</h6>
                            <h4 class="fw-bold mb-0 text-warning">{{ $unpaidInvoices }}</h4>
                        </div>
                    </div>
                </div>
                <div class="col-6">
                    <div class="card bg-info bg-opacity-10 border-info border-opacity-25 h-100">
                        <div class="card-body p-3 text-center">
                            <h6 class="text-info small mb-1">Recurring Invoices</h6>
                            <h4 class="fw-bold mb-0 text-info">{{ count($recurringInvoices) }}</h4>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Kim Recurring Focus Info -->


    <!-- Main Content -->
    <div class="row g-4">
        <!-- Recurring Invoices Section -->
        <div class="col-md-6">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white border-bottom d-flex justify-content-between align-items-center">
                    <h5 class="mb-0 fw-semibold">Upcoming Recurring Invoices</h5>
                    <a href="{{ route('invoices.create') }}" class="btn btn-primary btn-sm">
                        <i class="fas fa-plus me-1"></i>Create New
                    </a>
                </div>
                <div class="card-body p-0">
                    @php
                        // Filter only ACTIVE recurring invoices and sort by next run date
                        $activeRecurringInvoices = $recurringInvoices->filter(function($invoice) {
                            return $invoice->status === 'active' && $invoice->is_recurring;
                        })->sortBy(function($invoice) {
                            return $invoice->next_run_date ?? now()->addMonth();
                        });
                    @endphp

                    @if($activeRecurringInvoices->count() > 0)
                        @foreach($activeRecurringInvoices->take(10) as $index => $invoice)
                            @php
                                $nextRun = $invoice->next_run_date ?? now()->addMonth();
                                $interval = $invoice->recurring_settings['interval'] ?? 'monthly';
                                $rebillTitle = $invoice->recurring_settings['rebill_title'] ?? $invoice->title ?? $invoice->document_nr;
                                $rebillDescription = $invoice->recurring_settings['rebill_description'] ?? '';
                            @endphp

                            <a href="{{ route('invoices.show', $invoice->id) }}" class="text-decoration-none">
                                <div class="d-flex align-items-center p-3 {{ $index > 0 ? 'border-top' : '' }}"
                                     style="transition: background-color 0.15s; cursor: pointer;"
                                     onmouseover="this.style.backgroundColor='#f9f9f9'"
                                     onmouseout="this.style.backgroundColor='#fff'">

                                    <div class="me-3">
                                        <div class="bg-dark text-white rounded d-flex align-items-center justify-content-center flex-column"
                                             style="width: 45px; height: 45px; font-size: 0.75rem; line-height: 0.7;">
                                            <div class="fw-bold" style="line-height: 1.1;">{{ \Carbon\Carbon::parse($nextRun)->format('d') }}</div>
                                            <div style="font-size: 0.6rem; line-height: 1.1; text-transform: uppercase;">
                                                {{ \Carbon\Carbon::parse($nextRun)->format('M') }}
                                            </div>
                                        </div>
                                    </div>

                                    <div class="flex-grow-1">
                                        <h6 class="mb-1 fw-medium" style="font-size: 0.875rem; color: #000;">{{ $rebillTitle }}</h6>
                                        @if($rebillDescription)
                                            <small class="text-muted" style="font-size: 0.75rem;">{{ $rebillDescription }}</small>
                                        @endif

                                        <div class="mt-1 d-flex align-items-center" style="gap: 10px;">
                                            <span class="badge bg-success bg-opacity-10 text-success" style="font-size: 0.75rem;">
                                                Active
                                            </span>
                                            <span class="badge bg-light text-dark" style="font-size: 0.75rem;">
                                                {{ ucfirst($interval) }}
                                            </span>
                                        </div>
                                    </div>
                                </div>
                            </a>
                        @endforeach

                        <!-- View All Button -->
                        <div class="d-flex align-items-center justify-content-center" style="position: relative; width: 100%;">
                            <a href="{{ route('invoices.index') }}" class="btn btn-outline-secondary btn-sm"
                               style="position: absolute; bottom: -15px;">View All</a>
                        </div>
                    @else
                        <div class="d-flex align-items-center justify-content-center flex-column w-100 py-5">
                            <i class="fas fa-receipt fa-lg text-muted mb-2"></i>
                            <p class="fw-medium text-muted mb-0">You don't have any invoices</p>
                        </div>
                    @endif
                </div>
            </div>
        </div>

        <!-- Drafts Section -->
        <div class="col-md-6">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white border-bottom">
                    <h5 class="mb-0 fw-semibold">Drafts</h5>
                </div>
                <div class="card-body p-0">
                    @if(count($drafts) > 0)
                        @foreach($drafts->take(5) as $index => $draft)
                            <div class="d-flex align-items-center p-3 {{ $index > 0 ? 'border-top' : '' }}">
                                <div class="me-3">
                                    <i class="fas fa-file-alt fa-lg text-muted"></i>
                                </div>
                                <div class="flex-grow-1">
                                    <h6 class="mb-1">{{ $draft->title ?? $draft->document_nr ?? 'Draft Invoice' }}</h6>
                                    <small class="text-muted">#{{ $draft->id }}</small>
                                    @if(isset($draft->contact_info['name']))
                                        <small class="text-muted"> - {{ $draft->contact_info['name'] }}</small>
                                    @endif
                                    <div class="mt-1">
                                        <span class="badge bg-warning bg-opacity-10 text-warning">Draft</span>
                                        @if($draft->total > 0)
                                            <span class="badge bg-light text-dark ms-1">CHF {{ number_format($draft->total, 2) }}</span>
                                        @endif
                                        <small class="text-muted ms-2">{{ $draft->created_at->format('M j, Y') }}</small>
                                    </div>
                                </div>
                                <div class="text-end">
                                    <a href="{{ route('invoices.edit', $draft->id) }}" class="btn btn-sm btn-outline-primary me-1">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <form method="POST" action="{{ route('invoices.destroy', $draft->id) }}" class="d-inline" onsubmit="return confirm('Are you sure you want to delete this draft?')">
                                        @csrf
                                        @method('DELETE')
                                        <button type="submit" class="btn btn-sm btn-outline-danger">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </form>
                                </div>
                            </div>
                        @endforeach
                        @if(count($drafts) > 5)
                            <div class="text-center p-3 border-top">
                                <a href="{{ route('invoices.index') }}?status=draft" class="btn btn-outline-secondary btn-sm">View All</a>
                            </div>
                        @endif
                    @else
                        <div class="text-center py-5">
                            <i class="fas fa-file-alt fa-2x text-muted mb-3"></i>
                            <h6 class="text-muted">You don't have any drafts</h6>
                        </div>
                    @endif
                </div>


            </div>
        </div>
    </div>
</div>
@endsection
