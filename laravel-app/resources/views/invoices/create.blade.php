@extends('layouts.rebill')

@php
    $title = "Create New Invoice";
@endphp

@section('content')
<div class="row">
    <div class="col-12">
        <div class="d-flex align-items-center justify-content-between mb-4">
            <div class="d-flex align-items-center">
                <i class="fas fa-receipt me-2"></i>
                <h1 class="mb-0">Create New Invoice</h1>
            </div>
            <a href="{{ route('dashboard') }}" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left me-1"></i> Back
            </a>
        </div>

        <form id="invoice-form" action="{{ route('invoices.store') }}" method="POST">
            @csrf
            <input type="hidden" name="action" id="form-action" value="create">

            <!-- Invoice Details Section -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">Invoice Details</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="rebill_title" class="form-label">Title in reBill <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="rebill_title" name="rebill_title"
                                       placeholder="e.g. Construction Services..." required>
                                <div class="form-text">A short title to describe your invoice</div>
                            </div>
                            <div class="mb-3">
                                <label for="rebill_description" class="form-label">Description in reBill</label>
                                <textarea class="form-control" id="rebill_description" name="rebill_description" rows="3"
                                          placeholder="e.g. Ongoing development services..."></textarea>
                                <div class="form-text">Describe the products/services</div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="document_nr" class="form-label">Document Number <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="document_nr" name="document_nr"
                                       placeholder="e.g. INV-2025-001" required>
                                <div class="form-text">Unique document number for this invoice</div>
                            </div>
                            <div class="mb-3">
                                <label for="bexio_title" class="form-label">Title in Bexio</label>
                                <input type="text" class="form-control" id="bexio_title" name="bexio_title"
                                       placeholder="e.g. Construction Services...">
                                <div class="form-text">Title for the Bexio invoice</div>
                            </div>
                            <div class="mb-3">
                                <label for="bexio_reference" class="form-label">Reference in Bexio</label>
                                <textarea class="form-control" id="bexio_reference" name="bexio_reference" rows="3"
                                          placeholder="e.g. Ongoing development services..."></textarea>
                                <div class="form-text">Reference for the Bexio invoice</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Customer Selection Section -->
            <div class="card mb-4">
                <div class="card-header">
                    <div class="d-flex align-items-center justify-content-between">
                        <h5 class="mb-0">Select Customer</h5>
                        <div class="btn-group" role="group">
                            <input type="radio" class="btn-check" name="customer_type" id="existing_customer" value="existing" checked>
                            <label class="btn btn-outline-primary" for="existing_customer">Existing</label>

                            <input type="radio" class="btn-check" name="customer_type" id="new_customer" value="new">
                            <label class="btn btn-outline-primary" for="new_customer">New</label>
                        </div>
                    </div>
                    <small class="text-muted">Choose an existing customer or create a new one</small>
                </div>
                <div class="card-body">
                    <!-- Existing Customer -->
                    <div id="existing-customer-section">
                        <div class="mb-3">
                            <label for="customer_id" class="form-label">Customer <span class="text-danger">*</span></label>
                            <select class="form-select" id="customer_id" name="contact_id" required>
                                <option value="">Select customer...</option>
                                @if(isset($contacts))
                                    @foreach($contacts as $contact)
                                        <option value="{{ $contact['id'] }}"
                                                data-name="{{ $contact['name_1'] }}"
                                                data-email="{{ $contact['mail'] }}"
                                                data-address="{{ $contact['address'] ?? '' }}">
                                            {{ $contact['name_1'] }} ({{ $contact['mail'] }})
                                        </option>
                                    @endforeach
                                @endif
                            </select>
                        </div>
                    </div>

                    <!-- New Customer -->
                    <div id="new-customer-section" style="display: none;">
                        <div class="p-3 bg-light rounded">
                            <h6 class="fw-bold">New Customer</h6>
                            <hr>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="new_customer_name" class="form-label">Name <span class="text-danger">*</span></label>
                                        <input type="text" class="form-control" id="new_customer_name" name="new_customer_name"
                                               placeholder="John Doe">
                                    </div>
                                    <div class="mb-3">
                                        <label for="new_customer_email" class="form-label">Email <span class="text-danger">*</span></label>
                                        <input type="email" class="form-control" id="new_customer_email" name="new_customer_email"
                                               placeholder="<EMAIL>">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="new_customer_type" class="form-label">Customer Type <span class="text-danger">*</span></label>
                                        <select class="form-select" id="new_customer_type" name="new_customer_type">
                                            <option value="1">Company</option>
                                            <option value="2">Individual</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            <div class="mb-3">
                                <label for="new_customer_address" class="form-label">Address</label>
                                <input type="text" class="form-control" id="new_customer_address" name="new_customer_address"
                                       placeholder="123 Main Street">
                            </div>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="new_customer_city" class="form-label">City</label>
                                        <input type="text" class="form-control" id="new_customer_city" name="new_customer_city"
                                               placeholder="New York">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="new_customer_postcode" class="form-label">Postal Code</label>
                                        <input type="text" class="form-control" id="new_customer_postcode" name="new_customer_postcode"
                                               placeholder="10001">
                                    </div>
                                </div>
                            </div>
                            <button type="button" class="btn btn-success" id="create-customer-btn">
                                <i class="fas fa-plus me-1"></i> Create Customer
                            </button>
                            <small class="d-block text-muted mt-2">You can add more details in the Bexio dashboard</small>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Billing Section -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">Billing</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="billing_period" class="form-label">Billing Period <span class="text-danger">*</span></label>
                                <select class="form-select" id="billing_period" name="billing_period" required>
                                    <option value="monthly">Monthly</option>
                                    <option value="yearly">Yearly</option>
                                </select>
                                <div class="form-text">Your customer will be billed in this cycle</div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="start_date" class="form-label">Start Date <span class="text-danger">*</span></label>
                                <input type="date" class="form-control" id="start_date" name="start_date"
                                       min="{{ date('Y-m-d') }}" value="{{ date('Y-m-d') }}" required>
                                <div class="form-text">Invoices will be sent to your customer starting from this date</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Items Section -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">Line Items</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-bordered">
                            <thead class="table-light">
                                <tr>
                                    <th style="width: 30%">Name <small class="text-muted">(with variables: [year], [month], [nextyear], [nextmonth])</small></th>
                                    <th style="width: 12%">Quantity</th>
                                    <th style="width: 15%">Unit</th>
                                    <th style="width: 15%">Unit Price</th>
                                    <th style="width: 15%">Tax</th>
                                    <th style="width: 10%">Total</th>
                                    <th style="width: 3%"></th>
                                </tr>
                            </thead>
                            <tbody id="items-container">
                                <!-- Items will be added here dynamically -->
                            </tbody>
                        </table>
                    </div>

                    <button type="button" class="btn btn-outline-primary" id="add-item-btn">
                        <i class="fas fa-plus me-1"></i> Add Line Item
                    </button>

                    <hr>
                    <div class="text-end">
                        <h5>Total: <span id="total-amount">0.00</span> CHF</h5>
                    </div>
                </div>
            </div>

            <!-- Tax Section -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">Tax</h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <label for="tax_status" class="form-label">Tax Status <span class="text-danger">*</span></label>
                        <select class="form-select" id="tax_status" name="tax_status" required>
                            <option value="0">Tax Inclusive</option>
                            <option value="1">Tax Exclusive</option>
                            <option value="2">Tax Exempt</option>
                        </select>
                        <div class="form-text">Choose the tax status for this invoice</div>
                    </div>
                </div>
            </div>

            <!-- Hidden Fields for Form Submission -->
            <input type="hidden" id="contact_name" name="contact_name" value="">
            <input type="hidden" id="contact_email" name="contact_email" value="">
            <input type="hidden" id="contact_address" name="contact_address" value="">
            <input type="hidden" id="total" name="total" value="0">
            <input type="hidden" id="status" name="status" value="draft">

            <!-- Actions -->
            <div class="card">
                <div class="card-body">
                    <div class="d-flex gap-2">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-receipt me-1"></i> Create Invoice
                        </button>
                        <button type="button" class="btn btn-outline-secondary" id="save-draft-btn">
                            <i class="fas fa-save me-1"></i> Save as Draft
                        </button>
                    </div>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- Item Template (hidden) -->
<template id="item-template">
    <tr class="item-row">
        <td>
            <textarea class="form-control item-name" name="items[INDEX][name]" rows="2"
                      placeholder="Item name" required></textarea>
        </td>
        <td>
            <input type="number" class="form-control item-quantity" name="items[INDEX][quantity]"
                   min="0" step="0.01" value="1" required>
        </td>
        <td>
            <select class="form-select item-unit" name="items[INDEX][unit_id]" required>
                <option value="">Select unit...</option>
                <!-- Units will be loaded via AJAX -->
            </select>
        </td>
        <td>
            <input type="number" class="form-control item-price" name="items[INDEX][unit_price]"
                   min="0" step="0.01" value="0" required>
        </td>
        <td>
            <select class="form-select item-tax" name="items[INDEX][tax_id]" required>
                <option value="">Select tax...</option>
                <!-- Taxes will be loaded via AJAX -->
            </select>
        </td>
        <td>
            <div class="bg-light border rounded p-2 text-center">
                <span class="item-total">0.00</span>
            </div>
        </td>
        <td>
            <button type="button" class="btn btn-outline-danger btn-sm remove-item-btn">
                <i class="fas fa-trash"></i>
            </button>
        </td>
    </tr>
</template>
@endsection

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    let itemIndex = 0;

    // Customer type toggle
    document.querySelectorAll('input[name="customer_type"]').forEach(radio => {
        radio.addEventListener('change', function() {
            const existingSection = document.getElementById('existing-customer-section');
            const newSection = document.getElementById('new-customer-section');

            if (this.value === 'existing') {
                existingSection.style.display = 'block';
                newSection.style.display = 'none';
                document.getElementById('customer_id').required = true;
                document.getElementById('new_customer_name').required = false;
                document.getElementById('new_customer_email').required = false;
            } else {
                existingSection.style.display = 'none';
                newSection.style.display = 'block';
                document.getElementById('customer_id').required = false;
                document.getElementById('new_customer_name').required = true;
                document.getElementById('new_customer_email').required = true;
            }
        });
    });

    // Add item functionality
    document.getElementById('add-item-btn').addEventListener('click', function() {
        console.log('Add item button clicked');
        addItem();
    });

    function addItem() {
        console.log('Adding item with index:', itemIndex);

        const container = document.getElementById('items-container');

        if (!container) {
            console.error('Container not found');
            return;
        }

        // Create row HTML directly
        const rowHTML = `
            <tr class="item-row">
                <td>
                    <textarea class="form-control item-name" name="items[${itemIndex}][name]" rows="2"
                              placeholder="Item name" required></textarea>
                </td>
                <td>
                    <input type="number" class="form-control item-quantity" name="items[${itemIndex}][quantity]"
                           min="0" step="0.01" value="1" required>
                </td>
                <td>
                    <select class="form-select item-unit" name="items[${itemIndex}][unit_id]" required>
                        <option value="">Select unit...</option>
                    </select>
                </td>
                <td>
                    <input type="number" class="form-control item-price" name="items[${itemIndex}][unit_price]"
                           min="0" step="0.01" value="0" required>
                </td>
                <td>
                    <select class="form-select item-tax" name="items[${itemIndex}][tax_id]" required>
                        <option value="">Select tax...</option>
                    </select>
                </td>
                <td>
                    <div class="bg-light border rounded p-2 text-center">
                        <span class="item-total">0.00</span>
                    </div>
                </td>
                <td>
                    <button type="button" class="btn btn-outline-danger btn-sm remove-item-btn">
                        <i class="fas fa-trash"></i>
                    </button>
                </td>
            </tr>
        `;

        // Add row to container
        container.insertAdjacentHTML('beforeend', rowHTML);

        // Get the newly added row
        const newRow = container.lastElementChild;

        // Add event listeners for calculation
        addItemEventListeners(newRow);

        itemIndex++;
        console.log('Item added successfully. Total items:', itemIndex);

        // Load units and taxes for new item
        if (itemIndex === 1) {
            loadUnitsAndTaxes();
        } else {
            // Copy options from first item
            copySelectOptions(newRow);
        }

        calculateTotal();
    }

    function addItemEventListeners(itemRow) {
        const quantityInput = itemRow.querySelector('.item-quantity');
        const priceInput = itemRow.querySelector('.item-price');
        const removeBtn = itemRow.querySelector('.remove-item-btn');

        quantityInput.addEventListener('input', () => calculateItemTotal(itemRow));
        priceInput.addEventListener('input', () => calculateItemTotal(itemRow));

        removeBtn.addEventListener('click', function() {
            if (document.querySelectorAll('.item-row').length > 1) {
                itemRow.remove();
                calculateTotal();
            }
        });
    }

    function calculateItemTotal(itemRow) {
        const quantity = parseFloat(itemRow.querySelector('.item-quantity').value) || 0;
        const price = parseFloat(itemRow.querySelector('.item-price').value) || 0;
        const total = quantity * price;

        itemRow.querySelector('.item-total').textContent = total.toFixed(2);
        calculateTotal();
    }

    function calculateTotal() {
        let total = 0;
        document.querySelectorAll('.item-total').forEach(element => {
            total += parseFloat(element.textContent) || 0;
        });

        document.getElementById('total-amount').textContent = total.toFixed(2);

        // Update hidden total field for form submission
        document.getElementById('total').value = total.toFixed(2);
    }

    function copySelectOptions(itemRow) {
        const firstUnitSelect = document.querySelector('.item-unit');
        const firstTaxSelect = document.querySelector('.item-tax');

        if (firstUnitSelect) {
            const newUnitSelect = itemRow.querySelector('.item-unit');
            newUnitSelect.innerHTML = firstUnitSelect.innerHTML;
        }

        if (firstTaxSelect) {
            const newTaxSelect = itemRow.querySelector('.item-tax');
            newTaxSelect.innerHTML = firstTaxSelect.innerHTML;
        }
    }

    function loadUnitsAndTaxes() {
        // Load units
        fetch('/api/bexio/units')
            .then(response => response.json())
            .then(data => {
                const unitSelects = document.querySelectorAll('.item-unit');
                unitSelects.forEach(select => {
                    select.innerHTML = '<option value="">Select unit...</option>';
                    if (data && Array.isArray(data)) {
                        data.forEach(unit => {
                            select.innerHTML += `<option value="${unit.id}">${unit.name}</option>`;
                        });
                    }
                });
            })
            .catch(error => {
                console.error('Error loading units:', error);
                // Add default units
                const unitSelects = document.querySelectorAll('.item-unit');
                unitSelects.forEach(select => {
                    select.innerHTML = `
                        <option value="">Select unit...</option>
                        <option value="1">Pieces</option>
                        <option value="2">Hours</option>
                        <option value="3">Days</option>
                    `;
                });
            });

        // Load taxes
        fetch('/api/bexio/taxes')
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                return response.json();
            })
            .then(data => {
                const taxSelects = document.querySelectorAll('.item-tax');
                taxSelects.forEach(select => {
                    select.innerHTML = '<option value="">Select tax...</option>';

                    if (data.error) {
                        console.error('Tax API Error:', data.error);
                        return;
                    }

                    if (data && Array.isArray(data)) {
                        console.log(`Loaded ${data.length} taxes`);
                        data.forEach(tax => {
                            const displayName = tax.display_name || `${tax.percentage}% VAT` || tax.name;
                            select.innerHTML += `<option value="${tax.id}">${displayName}</option>`;
                        });
                    }
                });
            })
            .catch(error => {
                console.error('Error loading taxes:', error);
                // Add default taxes
                const taxSelects = document.querySelectorAll('.item-tax');
                taxSelects.forEach(select => {
                    select.innerHTML = `
                        <option value="">Select tax...</option>
                        <option value="1">7.7% VAT</option>
                        <option value="2">2.5% VAT</option>
                        <option value="3">0% VAT</option>
                    `;
                });
            });
    }

    // Load customers
    fetch('/api/bexio/customers')
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            return response.json();
        })
        .then(data => {
            const customerSelect = document.getElementById('customer_id');

            if (data.error) {
                console.error('API Error:', data.error);
                customerSelect.innerHTML += '<option value="">Error loading customers</option>';
                return;
            }

            if (data && Array.isArray(data)) {
                console.log(`Loaded ${data.length} customers`);

                // Store customer data for later use
                window.customerData = {};

                data.forEach(customer => {
                    const name = customer.name_1 || customer.name || 'Unknown';
                    const email = customer.mail ? ` (${customer.mail})` : '';

                    // Store full customer data
                    window.customerData[customer.id] = customer;

                    customerSelect.innerHTML += `<option value="${customer.id}">${name}${email}</option>`;
                });
            } else {
                console.warn('No customer data received');
                customerSelect.innerHTML += '<option value="">No customers found</option>';
            }
        })
        .catch(error => {
            console.error('Error loading customers:', error);
            const customerSelect = document.getElementById('customer_id');
            customerSelect.innerHTML += '<option value="">Failed to load customers</option>';
        });

    // Create customer functionality
    document.getElementById('create-customer-btn').addEventListener('click', function() {
        const name = document.getElementById('new_customer_name').value;
        const email = document.getElementById('new_customer_email').value;
        const type = document.getElementById('new_customer_type').value;
        const address = document.getElementById('new_customer_address').value;
        const city = document.getElementById('new_customer_city').value;
        const postcode = document.getElementById('new_customer_postcode').value;

        if (!name || !email) {
            alert('Name and Email are required');
            return;
        }

        const customerData = {
            name: name,
            email: email,
            type: parseInt(type),
            address: address,
            city: city,
            postcode: postcode
        };

        fetch('/api/bexio/create-customer', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            },
            body: JSON.stringify({ customer: customerData })
        })
        .then(response => response.json())
        .then(data => {
            if (data.error) {
                alert('Error creating customer: ' + data.message);
            } else {
                // Add new customer to select
                const customerSelect = document.getElementById('customer_id');
                customerSelect.innerHTML += `<option value="${data.id}" selected>${data.name}</option>`;

                // Switch to existing customer tab
                document.getElementById('existing_customer').checked = true;
                document.getElementById('existing_customer').dispatchEvent(new Event('change'));

                alert('Customer created successfully!');
            }
        })
        .catch(error => {
            console.error('Error creating customer:', error);
            alert('Error creating customer');
        });
    });

    // Customer selection handler
    document.getElementById('customer_id').addEventListener('change', function() {
        const selectedOption = this.options[this.selectedIndex];

        if (selectedOption.value) {
            // Get customer data from the selected option attributes
            const customer = {
                id: selectedOption.value,
                name_1: selectedOption.getAttribute('data-name'),
                mail: selectedOption.getAttribute('data-email'),
                address: selectedOption.getAttribute('data-address') || ''
            };

            // Update hidden fields with customer info
            document.getElementById('contact_name').value = customer.name_1 || '';
            document.getElementById('contact_email').value = customer.mail || '';
            document.getElementById('contact_address').value = customer.address || '';

            console.log('Selected customer:', customer.name_1, customer.mail);
        } else {
            // Clear hidden fields if no customer selected
            document.getElementById('contact_name').value = '';
            document.getElementById('contact_email').value = '';
            document.getElementById('contact_address').value = '';
        }
    });

    // Form validation before submit
    document.getElementById('invoice-form').addEventListener('submit', function(e) {
        const documentNr = document.getElementById('document_nr').value.trim();
        const contactName = document.getElementById('contact_name').value.trim();
        const contactEmail = document.getElementById('contact_email').value.trim();
        const total = parseFloat(document.getElementById('total').value) || 0;

        let errors = [];

        if (!documentNr) {
            errors.push('Document number is required');
        }

        if (!contactName) {
            errors.push('Please select a customer');
        }

        if (!contactEmail) {
            errors.push('Customer email is required');
        }

        if (total <= 0) {
            errors.push('Invoice total must be greater than 0');
        }

        // Check if at least one item exists
        const items = document.querySelectorAll('.item-row');
        if (items.length === 0) {
            errors.push('At least one line item is required');
        }

        if (errors.length > 0) {
            e.preventDefault();
            alert('Please fix the following errors:\n\n' + errors.join('\n'));
            return false;
        }

        console.log('Form validation passed, submitting...');
    });

    // Generate document number
    function generateDocumentNumber() {
        const now = new Date();
        const year = now.getFullYear();
        const month = String(now.getMonth() + 1).padStart(2, '0');
        const day = String(now.getDate()).padStart(2, '0');
        const time = String(now.getHours()).padStart(2, '0') + String(now.getMinutes()).padStart(2, '0');

        return `INV-${year}${month}${day}-${time}`;
    }

    // Set default document number
    document.getElementById('document_nr').value = generateDocumentNumber();

    // Add first item by default
    addItem();

    // Handle Save Draft button
    document.getElementById('save-draft-btn').addEventListener('click', function() {
        // Set action to save_draft
        document.getElementById('form-action').value = 'save_draft';

        // Submit the form
        document.getElementById('invoice-form').submit();
    });
});
</script>
@endpush
