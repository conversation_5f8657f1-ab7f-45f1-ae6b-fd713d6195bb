@extends('layouts.rebill')

@php
    $title = "Edit Invoice";
@endphp

@section('content')
<div class="row">
    <div class="col-12">
        <div class="d-flex align-items-center justify-content-between mb-4">
            <div class="d-flex align-items-center">
                <i class="fas fa-receipt me-2"></i>
                <h1 class="mb-0">
                    @if($invoice->status === 'draft')
                        Editing draft #{{ $invoice->id }}
                    @else
                        Editing recurring invoice ({{ $invoice->id }})
                    @endif
                </h1>
            </div>
            <a href="{{ url()->previous() }}" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left me-1"></i> Back
            </a>
        </div>

        <form id="invoice-form" action="{{ route('invoices.update', $invoice) }}" method="POST">
            @csrf
            @method('PUT')

            <!-- Invoice Details Section -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">Invoice Details</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="rebill_title" class="form-label">Title in reBill <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="rebill_title" name="rebill_title"
                                       value="{{ old('rebill_title', $invoice->recurring_settings['rebill_title'] ?? $invoice->title) }}"
                                       placeholder="e.g. Construction Services..." required>
                                <div class="form-text">A short title to describe your invoice</div>
                            </div>
                            <div class="mb-3">
                                <label for="rebill_description" class="form-label">Description in reBill</label>
                                <textarea class="form-control" id="rebill_description" name="rebill_description" rows="3"
                                          placeholder="e.g. Ongoing development services...">{{ old('rebill_description', $invoice->recurring_settings['rebill_description'] ?? '') }}</textarea>
                                <div class="form-text">Describe the products/services</div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="title" class="form-label">Title in Bexio</label>
                                <input type="text" class="form-control" id="title" name="title"
                                       value="{{ old('title', $invoice->recurring_settings['bexio_title'] ?? '') }}"
                                       placeholder="e.g. Construction Services...">
                                <div class="form-text">A short title to describe your invoice</div>
                            </div>
                            <div class="mb-3">
                                <label for="reference" class="form-label">Reference in Bexio</label>
                                <textarea class="form-control" id="reference" name="reference" rows="3"
                                          placeholder="e.g. Ongoing development services...">{{ old('reference', $invoice->recurring_settings['bexio_reference'] ?? '') }}</textarea>
                                <div class="form-text">Describe the product/services you're providing</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Customer Information -->
            <div class="card mb-4">
                <div class="card-header">
                    <div class="d-flex align-items-center justify-content-between">
                        <h5 class="mb-0">Customer Information</h5>
                        <div class="btn-group btn-group-sm" role="group">
                            <input type="radio" class="btn-check" name="customer_type" id="existing_customer" value="existing" checked>
                            <label class="btn btn-outline-primary" for="existing_customer">Existing Customer</label>

                            <input type="radio" class="btn-check" name="customer_type" id="new_customer" value="new">
                            <label class="btn btn-outline-primary" for="new_customer">New Customer</label>
                        </div>
                    </div>
                    <small class="text-muted">Choose an existing customer or create a new one</small>
                </div>
                <div class="card-body">
                    <!-- Existing Customer -->
                    <div id="existing-customer-section">
                        <div class="mb-3">
                            <label for="customer_id" class="form-label">Customer <span class="text-danger">*</span></label>
                            <select class="form-select" id="customer_id" name="customer_id" required>
                                <option value="">Select customer...</option>
                                <!-- Options will be loaded via AJAX -->
                            </select>
                        </div>

                        <!-- Hidden fields to store current customer data for fallback -->
                        <input type="hidden" id="contact_name" name="contact_name" value="{{ old('contact_name', $invoice->contact_info['name'] ?? '') }}">
                        <input type="hidden" id="contact_email" name="contact_email" value="{{ old('contact_email', $invoice->contact_info['email'] ?? '') }}">
                        <input type="hidden" id="contact_address" name="contact_address" value="{{ old('contact_address', $invoice->contact_info['address'] ?? '') }}">
                    </div>

                    <!-- New Customer -->
                    <div id="new-customer-section" style="display: none;">
                        <div class="p-3 bg-light rounded">
                            <h6 class="fw-bold">New Customer</h6>
                            <hr>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="new_customer_name" class="form-label">Name <span class="text-danger">*</span></label>
                                        <input type="text" class="form-control" id="new_customer_name" name="new_customer_name"
                                               placeholder="John Doe">
                                    </div>
                                    <div class="mb-3">
                                        <label for="new_customer_email" class="form-label">Email <span class="text-danger">*</span></label>
                                        <input type="email" class="form-control" id="new_customer_email" name="new_customer_email"
                                               placeholder="<EMAIL>">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="new_customer_type" class="form-label">Customer Type <span class="text-danger">*</span></label>
                                        <select class="form-select" id="new_customer_type" name="new_customer_type">
                                            <option value="1">Company</option>
                                            <option value="2">Individual</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            <div class="mb-3">
                                <label for="new_customer_address" class="form-label">Address</label>
                                <input type="text" class="form-control" id="new_customer_address" name="new_customer_address"
                                       placeholder="123 Main Street">
                            </div>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="new_customer_city" class="form-label">City</label>
                                        <input type="text" class="form-control" id="new_customer_city" name="new_customer_city"
                                               placeholder="New York">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="new_customer_postcode" class="form-label">Postal Code</label>
                                        <input type="text" class="form-control" id="new_customer_postcode" name="new_customer_postcode"
                                               placeholder="10001">
                                    </div>
                                </div>
                            </div>
                            <button type="button" class="btn btn-success" id="create-customer-btn">
                                <i class="fas fa-plus me-1"></i> Create Customer
                            </button>
                            <small class="d-block text-muted mt-2">You can add more details in the Bexio dashboard</small>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Billing Configuration -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">Billing</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="billing_period" class="form-label">Billing Period <span class="text-danger">*</span></label>
                                <select class="form-select" id="billing_period" name="billing_period" required>
                                    @php
                                        $currentInterval = old('billing_period', $invoice->recurring_settings['interval'] ?? 'monthly');
                                    @endphp
                                    <option value="monthly" {{ $currentInterval == 'monthly' ? 'selected' : '' }}>Monthly</option>
                                    <option value="yearly" {{ $currentInterval == 'yearly' ? 'selected' : '' }}>Yearly</option>
                                </select>
                                <div class="form-text">Your customer will be billed in this cycle</div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="start_date" class="form-label">Start Date <span class="text-danger">*</span></label>
                                @php
                                    $startDate = old('start_date');
                                    if (!$startDate && $invoice->recurringTemplate) {
                                        $startDate = $invoice->recurringTemplate->next_run;
                                    } elseif (!$startDate && isset($invoice->recurring_settings['next_charge'])) {
                                        $startDate = $invoice->recurring_settings['next_charge'];
                                    }
                                    if ($startDate) {
                                        $startDate = \Carbon\Carbon::parse($startDate)->format('Y-m-d');
                                    }
                                @endphp
                                <input type="date" class="form-control" id="start_date" name="start_date"
                                       value="{{ $startDate }}" required>
                                <div class="form-text">Invoices will be sent to your customer starting from this date</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Items Section -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">Line Items</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-bordered">
                            <thead class="table-light">
                                <tr>
                                    <th style="width: 30%">Name <small class="text-muted">(with variables: [year], [month], [nextyear], [nextmonth])</small></th>
                                    <th style="width: 12%">Quantity</th>
                                    <th style="width: 15%">Unit</th>
                                    <th style="width: 15%">Unit Price</th>
                                    <th style="width: 15%">Tax</th>
                                    <th style="width: 10%">Total</th>
                                    <th style="width: 3%"></th>
                                </tr>
                            </thead>
                            <tbody id="items-container">
                                @php
                                    $items = $invoice->items ?? [];
                                    if (empty($items)) {
                                        // Default empty item if no items exist
                                        $items = [['name' => '', 'quantity' => 1, 'unit_id' => '', 'unit_price' => 0, 'tax_id' => '']];
                                    }
                                @endphp

                                @foreach($items as $index => $item)
                                <tr class="item-row">
                                    <td>
                                        <input type="text" class="form-control item-name" name="items[{{ $index }}][name]"
                                               value="{{ old('items.'.$index.'.name', $item['name'] ?? '') }}"
                                               placeholder="e.g. Development Services [month] [year]" required>
                                    </td>
                                    <td>
                                        <input type="number" class="form-control item-quantity" name="items[{{ $index }}][quantity]"
                                               value="{{ old('items.'.$index.'.quantity', $item['quantity'] ?? 1) }}"
                                               min="1" step="1" required>
                                    </td>
                                    <td>
                                        <select class="form-select item-unit" name="items[{{ $index }}][unit_id]"
                                                data-value="{{ old('items.'.$index.'.unit_id', $item['unit_id'] ?? '') }}" required>
                                            <option value="">Select unit...</option>
                                            <!-- Options will be loaded via AJAX -->
                                        </select>
                                    </td>
                                    <td>
                                        <input type="number" class="form-control item-price" name="items[{{ $index }}][unit_price]"
                                               value="{{ old('items.'.$index.'.unit_price', $item['unit_price'] ?? 0) }}"
                                               min="0" step="0.01" required>
                                    </td>
                                    <td>
                                        <select class="form-select item-tax" name="items[{{ $index }}][tax_id]"
                                                data-value="{{ old('items.'.$index.'.tax_id', $item['tax_id'] ?? '') }}" required>
                                            <option value="">Select tax...</option>
                                            <!-- Options will be loaded via AJAX -->
                                        </select>
                                    </td>
                                    <td>
                                        <span class="item-total">{{ number_format(($item['quantity'] ?? 1) * ($item['unit_price'] ?? 0), 2) }}</span>
                                    </td>
                                    <td>
                                        @if($index > 0)
                                        <button type="button" class="btn btn-sm btn-outline-danger remove-item-btn">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                        @endif
                                    </td>
                                </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>

                    <button type="button" class="btn btn-outline-primary" id="add-item-btn">
                        <i class="fas fa-plus me-1"></i> Add Line Item
                    </button>

                    <hr>
                    <div class="text-end">
                        <h5>Total: <span id="total-amount">{{ number_format($invoice->total ?? 0, 2) }}</span> CHF</h5>
                    </div>
                </div>
            </div>

            <!-- Tax Section -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">Tax</h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <label for="tax_status" class="form-label">Tax Status <span class="text-danger">*</span></label>
                        <select class="form-select" id="tax_status" name="tax_status" required>
                            @php
                                $currentTaxStatus = old('tax_status', $invoice->tax_status ?? 0);
                            @endphp
                            <option value="0" {{ $currentTaxStatus == 0 ? 'selected' : '' }}>Tax Inclusive</option>
                            <option value="1" {{ $currentTaxStatus == 1 ? 'selected' : '' }}>Tax Exclusive</option>
                            <option value="2" {{ $currentTaxStatus == 2 ? 'selected' : '' }}>Tax Exempt</option>
                        </select>
                        <div class="form-text">Choose the tax status for this invoice</div>
                    </div>
                </div>
            </div>

            <!-- Hidden Fields -->
            <input type="hidden" id="total" name="total" value="{{ $invoice->total ?? 0 }}">

            <!-- Actions -->
            <div class="d-flex align-items-center" style="gap: 10px; margin-top: 20px;">
                <button type="submit" class="btn btn-primary" style="padding: 0.5rem 1rem;">
                    <i class="fas fa-receipt me-2"></i>Save Changes
                </button>
                <a href="{{ url()->previous() }}" class="btn btn-outline-secondary" style="padding: 0.5rem 1rem;">
                    <i class="fas fa-trash me-2"></i>Discard Changes
                </a>
            </div>
        </form>
    </div>
</div>

@if ($errors->any())
    <div class="alert alert-danger mt-3">
        <ul class="mb-0">
            @foreach ($errors->all() as $error)
                <li>{{ $error }}</li>
            @endforeach
        </ul>
    </div>
@endif

<!-- Item Template (hidden) -->
<template id="item-template">
    <tr class="item-row">
        <td>
            <input type="text" class="form-control item-name" name="items[INDEX][name]"
                   placeholder="e.g. Development Services [month] [year]" required>
        </td>
        <td>
            <input type="number" class="form-control item-quantity" name="items[INDEX][quantity]"
                   min="1" step="1" value="1" required>
        </td>
        <td>
            <select class="form-select item-unit" name="items[INDEX][unit_id]" required>
                <option value="">Select unit...</option>
            </select>
        </td>
        <td>
            <input type="number" class="form-control item-price" name="items[INDEX][unit_price]"
                   min="0" step="0.01" value="0" required>
        </td>
        <td>
            <select class="form-select item-tax" name="items[INDEX][tax_id]" required>
                <option value="">Select tax...</option>
            </select>
        </td>
        <td>
            <span class="item-total">0.00</span>
        </td>
        <td>
            <button type="button" class="btn btn-sm btn-outline-danger remove-item-btn">
                <i class="fas fa-trash"></i>
            </button>
        </td>
    </tr>
</template>

@endsection

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    let itemIndex = document.querySelectorAll('.item-row').length;

    // Load units and taxes for existing items
    loadUnitsAndTaxes();

    // Load customers
    loadCustomers();

    // Add item functionality
    document.getElementById('add-item-btn').addEventListener('click', addItem);

    // Customer type switching
    document.getElementById('existing_customer').addEventListener('change', function() {
        if (this.checked) {
            document.getElementById('existing-customer-section').style.display = 'block';
            document.getElementById('new-customer-section').style.display = 'none';
            document.getElementById('customer_id').required = true;
            document.getElementById('new_customer_name').required = false;
            document.getElementById('new_customer_email').required = false;
        }
    });

    document.getElementById('new_customer').addEventListener('change', function() {
        if (this.checked) {
            document.getElementById('existing-customer-section').style.display = 'none';
            document.getElementById('new-customer-section').style.display = 'block';
            document.getElementById('customer_id').required = false;
            document.getElementById('new_customer_name').required = true;
            document.getElementById('new_customer_email').required = true;
        }
    });

    // Create customer functionality
    document.getElementById('create-customer-btn').addEventListener('click', createCustomer);

    // Customer selection change
    document.getElementById('customer_id').addEventListener('change', function() {
        const selectedCustomerId = this.value;

        if (selectedCustomerId && selectedCustomerId !== 'current' && window.customerData && window.customerData[selectedCustomerId]) {
            const customer = window.customerData[selectedCustomerId];

            // Update hidden fields with selected customer data
            document.getElementById('contact_name').value = customer.name_1 || '';
            document.getElementById('contact_email').value = customer.mail || '';

            // Build address from customer data
            let address = '';
            if (customer.address) address += customer.address;
            if (customer.city) address += (address ? ', ' : '') + customer.city;
            if (customer.postcode) address += (address ? ' ' : '') + customer.postcode;

            document.getElementById('contact_address').value = address;
        }
    });

    // Remove item functionality
    document.addEventListener('click', function(e) {
        if (e.target.closest('.remove-item-btn')) {
            e.target.closest('.item-row').remove();
            calculateTotal();
        }
    });

    // Calculate total when quantity or price changes
    document.addEventListener('input', function(e) {
        if (e.target.classList.contains('item-quantity') || e.target.classList.contains('item-price')) {
            calculateItemTotal(e.target.closest('.item-row'));
            calculateTotal();
        }
    });

    function addItem() {
        const template = document.getElementById('item-template');
        const clone = template.content.cloneNode(true);

        // Replace INDEX with actual index
        clone.querySelectorAll('[name*="INDEX"]').forEach(input => {
            input.name = input.name.replace('INDEX', itemIndex);
        });

        document.getElementById('items-container').appendChild(clone);

        // Copy options from first item if exists
        const firstRow = document.querySelector('.item-row');
        if (firstRow) {
            const newRow = document.querySelector('.item-row:last-child');
            copySelectOptions(newRow, firstRow);
        }

        itemIndex++;
        calculateTotal();
    }

    function copySelectOptions(newRow, firstRow) {
        // Copy unit options
        const firstUnitSelect = firstRow.querySelector('.item-unit');
        const newUnitSelect = newRow.querySelector('.item-unit');
        if (firstUnitSelect && newUnitSelect) {
            newUnitSelect.innerHTML = firstUnitSelect.innerHTML;
        }

        // Copy tax options
        const firstTaxSelect = firstRow.querySelector('.item-tax');
        const newTaxSelect = newRow.querySelector('.item-tax');
        if (firstTaxSelect && newTaxSelect) {
            newTaxSelect.innerHTML = firstTaxSelect.innerHTML;
        }
    }

    function calculateItemTotal(row) {
        const quantity = parseFloat(row.querySelector('.item-quantity').value) || 0;
        const price = parseFloat(row.querySelector('.item-price').value) || 0;
        const total = quantity * price;
        row.querySelector('.item-total').textContent = total.toFixed(2);
    }

    function calculateTotal() {
        let total = 0;
        document.querySelectorAll('.item-row').forEach(row => {
            calculateItemTotal(row);
            const itemTotal = parseFloat(row.querySelector('.item-total').textContent) || 0;
            total += itemTotal;
        });

        document.getElementById('total-amount').textContent = total.toFixed(2);
        document.getElementById('total').value = total.toFixed(2);
    }

    function loadUnitsAndTaxes() {
        console.log('Starting to load units and taxes...');

        // Load units
        console.log('Fetching units from /api/bexio/units');
        fetch('/api/bexio/units')
            .then(response => {
                console.log('Units response status:', response.status);
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                return response.json();
            })
            .then(data => {
                console.log('Units data received:', data);
                const unitSelects = document.querySelectorAll('.item-unit');
                console.log('Found unit selects:', unitSelects.length);

                unitSelects.forEach((select, index) => {
                    const currentValue = select.dataset.value || '';
                    console.log(`Processing unit select ${index}, current value: "${currentValue}"`);
                    select.innerHTML = '<option value="">Select unit...</option>';

                    if (data && Array.isArray(data)) {
                        console.log(`Processing ${data.length} units for select ${index}`);
                        data.forEach(unit => {
                            const option = document.createElement('option');
                            option.value = unit.id;
                            option.textContent = unit.name;
                            if (unit.id == currentValue) {
                                option.selected = true;
                                console.log(`Selected unit: ${unit.name} (ID: ${unit.id})`);
                            }
                            select.appendChild(option);
                        });
                    } else {
                        console.warn('No unit data received or invalid format:', data);
                    }
                });
            })
            .catch(error => {
                console.error('Error loading units:', error);
                // Add default units as fallback
                const unitSelects = document.querySelectorAll('.item-unit');
                unitSelects.forEach(select => {
                    select.innerHTML = `
                        <option value="">Select unit...</option>
                        <option value="1">Pieces</option>
                        <option value="2">Hours</option>
                        <option value="3">Days</option>
                        <option value="4">Months</option>
                        <option value="5">Years</option>
                    `;
                });
            });

        // Load taxes
        console.log('Fetching taxes from /api/bexio/taxes');
        fetch('/api/bexio/taxes')
            .then(response => {
                console.log('Taxes response status:', response.status);
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                return response.json();
            })
            .then(data => {
                console.log('Taxes data received:', data);
                const taxSelects = document.querySelectorAll('.item-tax');
                console.log('Found tax selects:', taxSelects.length);

                taxSelects.forEach((select, index) => {
                    const currentValue = select.dataset.value || '';
                    console.log(`Processing tax select ${index}, current value: "${currentValue}"`);
                    select.innerHTML = '<option value="">Select tax...</option>';

                    if (data && Array.isArray(data)) {
                        console.log(`Processing ${data.length} taxes for select ${index}`);
                        data.forEach(tax => {
                            const option = document.createElement('option');
                            option.value = tax.id;
                            option.textContent = tax.display_name || `${tax.percentage}% VAT` || tax.name;
                            if (tax.id == currentValue) {
                                option.selected = true;
                                console.log(`Selected tax: ${option.textContent} (ID: ${tax.id})`);
                            }
                            select.appendChild(option);
                        });
                    } else {
                        console.warn('No tax data received or invalid format:', data);
                    }
                });
            })
            .catch(error => {
                console.error('Error loading taxes:', error);
                // Add default taxes as fallback
                const taxSelects = document.querySelectorAll('.item-tax');
                taxSelects.forEach(select => {
                    select.innerHTML = `
                        <option value="">Select tax...</option>
                        <option value="1">7.7% VAT</option>
                        <option value="2">2.5% VAT</option>
                        <option value="3">0% VAT</option>
                        <option value="4">3.7% VAT</option>
                    `;
                });
            });
    }

    function loadCustomers() {
        fetch('/api/bexio/customers')
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                return response.json();
            })
            .then(data => {
                const customerSelect = document.getElementById('customer_id');
                const currentCustomerName = document.getElementById('contact_name').value;
                const currentCustomerEmail = document.getElementById('contact_email').value;

                if (data.error) {
                    console.error('API Error:', data.error);
                    customerSelect.innerHTML += '<option value="">Error loading customers</option>';
                    return;
                }

                if (data && Array.isArray(data)) {
                    console.log(`Loaded ${data.length} customers`);

                    // Store customer data for later use
                    window.customerData = {};

                    let selectedCustomerId = null;

                    data.forEach(customer => {
                        const name = customer.name_1 || 'Unknown';
                        const email = customer.mail ? ` (${customer.mail})` : '';

                        // Store full customer data
                        window.customerData[customer.id] = customer;

                        const option = document.createElement('option');
                        option.value = customer.id;
                        option.textContent = `${name}${email}`;

                        // Check if this customer matches the current invoice customer
                        if (customer.name_1 === currentCustomerName || customer.mail === currentCustomerEmail) {
                            option.selected = true;
                            selectedCustomerId = customer.id;
                        }

                        customerSelect.appendChild(option);
                    });

                    // If no exact match found, add current customer as a custom option
                    if (!selectedCustomerId && currentCustomerName) {
                        const customOption = document.createElement('option');
                        customOption.value = 'current';
                        customOption.textContent = `${currentCustomerName} (Current)`;
                        customOption.selected = true;
                        customerSelect.appendChild(customOption);
                    }
                } else {
                    console.warn('No customer data received');
                    customerSelect.innerHTML += '<option value="">No customers found</option>';
                }
            })
            .catch(error => {
                console.error('Error loading customers:', error);
                const customerSelect = document.getElementById('customer_id');
                customerSelect.innerHTML += '<option value="">Failed to load customers</option>';
            });
    }

    function createCustomer() {
        const name = document.getElementById('new_customer_name').value;
        const email = document.getElementById('new_customer_email').value;
        const type = document.getElementById('new_customer_type').value;
        const address = document.getElementById('new_customer_address').value;
        const city = document.getElementById('new_customer_city').value;
        const postcode = document.getElementById('new_customer_postcode').value;

        if (!name || !email) {
            alert('Please fill in name and email fields');
            return;
        }

        const customerData = {
            name_1: name,
            mail: email,
            contact_type_id: parseInt(type),
            address: address,
            city: city,
            postcode: postcode
        };

        fetch('/api/bexio/create-customer', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            },
            body: JSON.stringify(customerData)
        })
        .then(response => response.json())
        .then(data => {
            if (data.error) {
                alert('Error creating customer: ' + data.message);
            } else {
                // Add new customer to select
                const customerSelect = document.getElementById('customer_id');
                const option = document.createElement('option');
                option.value = data.id;
                option.textContent = `${data.name_1} (${data.mail})`;
                option.selected = true;
                customerSelect.appendChild(option);

                // Switch to existing customer tab
                document.getElementById('existing_customer').checked = true;
                document.getElementById('existing-customer-section').style.display = 'block';
                document.getElementById('new-customer-section').style.display = 'none';

                // Update hidden fields
                document.getElementById('contact_name').value = data.name_1;
                document.getElementById('contact_email').value = data.mail;
                document.getElementById('contact_address').value = data.address || '';

                alert('Customer created successfully!');
            }
        })
        .catch(error => {
            console.error('Error creating customer:', error);
            alert('Error creating customer. Please try again.');
        });
    }

    // Initial calculation
    calculateTotal();
});
</script>
@endpush
