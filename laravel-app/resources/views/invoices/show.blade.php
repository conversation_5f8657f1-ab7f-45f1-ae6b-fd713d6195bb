@extends('layouts.rebill')

@php
    $title = "Invoice #" . $invoice->id;
@endphp

@section('content')
<div class="row">
    <div class="col-12">
        <!-- Header Section -->
        <div class="d-flex align-items-center justify-content-between mb-4">
            <div class="d-flex align-items-center">
                <a href="{{ url()->previous() }}" class="btn btn-outline-secondary me-3">
                    <i class="fas fa-arrow-left"></i>
                </a>
                <div class="d-flex align-items-center">
                    <i class="fas fa-receipt me-2"></i>
                    <h1 class="mb-0">Invoice {{ $invoice->id }}</h1>
                </div>
            </div>
            
            <!-- Actions Dropdown -->
            <div class="dropdown">
                <button class="btn btn-outline-primary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                    <i class="fas fa-chevron-down me-1"></i> Actions
                </button>
                <ul class="dropdown-menu dropdown-menu-end">
                    <li>
                        <a class="dropdown-item" href="{{ route('invoices.edit', $invoice) }}">
                            <i class="fas fa-edit me-2"></i> Edit Invoice
                        </a>
                    </li>
                    @if($invoice->bexio_id)
                    <li>
                        <a class="dropdown-item" href="https://office.bexio.com/index.php/kb_invoice/show/id/{{ $invoice->bexio_id }}" target="_blank">
                            <i class="fas fa-external-link-alt me-2"></i> View in Bexio
                        </a>
                    </li>
                    @endif
                    <li><hr class="dropdown-divider"></li>
                    <li>
                        <form action="{{ route('invoices.destroy', $invoice) }}" method="POST" class="d-inline" 
                              onsubmit="return confirm('Are you sure you want to delete this invoice?')">
                            @csrf
                            @method('DELETE')
                            <button type="submit" class="dropdown-item text-danger">
                                <i class="fas fa-trash me-2"></i> Delete Invoice
                            </button>
                        </form>
                    </li>
                </ul>
            </div>
        </div>

        <hr class="mb-4">

        <!-- Invoice Status and Title -->
        <div class="d-flex align-items-center justify-content-between mb-4">
            <div>
                @if($invoice->status === 'active')
                    <span class="badge bg-success mb-2">Active</span>
                @elseif($invoice->status === 'draft')
                    <span class="badge bg-secondary mb-2">Draft</span>
                @else
                    <span class="badge bg-warning mb-2">{{ ucfirst($invoice->status) }}</span>
                @endif
                
                <h2 class="mb-1">{{ $invoice->title ?? 'Untitled Invoice' }}</h2>
                <p class="text-muted mb-0">
                    Document: {{ $invoice->document_nr }} • 
                    @if($invoice->is_recurring && $invoice->recurringTemplate)
                        Billed {{ $invoice->recurringTemplate->interval }} to customer
                    @else
                        One-time invoice
                    @endif
                </p>
            </div>
        </div>

        <!-- Billing History Section (for recurring invoices) -->
        @if($invoice->is_recurring && $invoice->recurringTemplate)
        <div class="card mb-4">
            <div class="card-header">
                <div class="d-flex align-items-center justify-content-between">
                    <div class="d-flex align-items-center">
                        <i class="fas fa-history me-2"></i>
                        <h6 class="mb-0 fw-semibold">Billing History</h6>
                    </div>
                    <small class="text-muted">
                        Next charge: {{ $invoice->recurringTemplate->next_run->format('M d, Y') }}
                    </small>
                </div>
            </div>
            <div class="card-body">
                <p class="text-muted mb-0">
                    <i class="fas fa-info-circle me-1"></i>
                    This is a recurring invoice that will be automatically processed {{ $invoice->recurringTemplate->interval }}.
                </p>
            </div>
        </div>
        @endif

        <!-- Items Section -->
        <div class="card mb-4">
            <div class="card-header">
                <div class="d-flex align-items-center justify-content-between">
                    <div class="d-flex align-items-center">
                        <i class="fas fa-box me-2"></i>
                        <h6 class="mb-0 fw-semibold">Items</h6>
                    </div>
                    <a href="{{ route('invoices.edit', $invoice) }}" class="btn btn-sm btn-outline-primary">
                        <i class="fas fa-edit me-1"></i> Edit
                    </a>
                </div>
            </div>
            
            @if($invoice->items && count($invoice->items) > 0)
            <div class="table-responsive">
                <table class="table table-sm mb-0">
                    <thead class="table-light">
                        <tr>
                            <th>Name</th>
                            <th>Quantity</th>
                            <th>Unit</th>
                            <th>Unit Price</th>
                            <th>Tax</th>
                            <th class="text-end">Total</th>
                        </tr>
                    </thead>
                    <tbody>
                        @php $subtotal = 0; @endphp
                        @foreach($invoice->items as $item)
                        @php 
                            $itemTotal = ($item['quantity'] ?? 1) * ($item['unit_price'] ?? 0);
                            $subtotal += $itemTotal;
                        @endphp
                        <tr>
                            <td>{{ $item['name'] ?? 'Unnamed Item' }}</td>
                            <td>{{ $item['quantity'] ?? 1 }}</td>
                            <td>
                                @if(isset($item['unit_name']))
                                    {{ $item['unit_name'] }}
                                @else
                                    <span class="text-muted">-</span>
                                @endif
                            </td>
                            <td>CHF {{ number_format($item['unit_price'] ?? 0, 2) }}</td>
                            <td>
                                @if(isset($item['tax_rate']))
                                    {{ $item['tax_rate'] }}%
                                @else
                                    <span class="text-muted">-</span>
                                @endif
                            </td>
                            <td class="text-end">CHF {{ number_format($itemTotal, 2) }}</td>
                        </tr>
                        @endforeach
                    </tbody>
                    <tfoot class="table-light">
                        <tr>
                            <th colspan="5" class="text-end">Total:</th>
                            <th class="text-end">CHF {{ number_format($invoice->total, 2) }}</th>
                        </tr>
                    </tfoot>
                </table>
            </div>
            @else
            <div class="card-body text-center text-muted">
                <i class="fas fa-box-open fa-2x mb-2"></i>
                <p class="mb-0">No items added to this invoice yet.</p>
            </div>
            @endif
        </div>

        <!-- Tax Status Section -->
        <div class="card mb-4">
            <div class="card-body">
                <div class="d-flex align-items-center justify-content-between">
                    <div>
                        <h6 class="fw-semibold mb-1">Tax Status</h6>
                        <small class="text-muted">This is applied to all line items</small>
                    </div>
                    <div>
                        @if($invoice->tax_status === 0)
                            <span class="badge bg-success">Inclusive</span>
                        @elseif($invoice->tax_status === 1)
                            <span class="badge bg-primary">Exclusive</span>
                        @else
                            <span class="badge bg-secondary">Exempt</span>
                        @endif
                    </div>
                </div>
            </div>
        </div>

        <!-- Customer Section -->
        <div class="card">
            <div class="card-header">
                <div class="d-flex align-items-center">
                    <i class="fas fa-user me-2"></i>
                    <h6 class="mb-0 fw-semibold">Customer</h6>
                </div>
            </div>
            <div class="card-body">
                @if($invoice->contact_info)
                <div class="d-flex align-items-start">
                    <div class="me-3">
                        <div class="bg-primary text-white rounded-circle d-flex align-items-center justify-content-center" 
                             style="width: 45px; height: 45px;">
                            <i class="fas fa-user"></i>
                        </div>
                    </div>
                    <div>
                        <h6 class="mb-1">
                            {{ $invoice->contact_info['name'] ?? 'Unknown Customer' }}
                        </h6>
                        <div class="text-muted small">
                            @if(isset($invoice->contact_info['email']))
                                <i class="fas fa-envelope me-1"></i>{{ $invoice->contact_info['email'] }}
                            @endif
                            @if(isset($invoice->contact_info['phone']) && isset($invoice->contact_info['email']))
                                <span class="mx-2">•</span>
                            @endif
                            @if(isset($invoice->contact_info['phone']))
                                <i class="fas fa-phone me-1"></i>{{ $invoice->contact_info['phone'] }}
                            @endif
                        </div>
                        @if(isset($invoice->contact_info['address']))
                        <div class="text-muted small mt-1">
                            <i class="fas fa-map-marker-alt me-1"></i>{{ $invoice->contact_info['address'] }}
                        </div>
                        @endif
                    </div>
                </div>
                @else
                <div class="text-center text-muted">
                    <i class="fas fa-user-slash fa-2x mb-2"></i>
                    <p class="mb-0">No customer information available</p>
                </div>
                @endif
            </div>
        </div>
    </div>
</div>
@endsection
