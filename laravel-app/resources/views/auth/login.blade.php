@extends('layouts.rebill')

@php
    $title = "Welcome to reBill";
@endphp

@section('content')
<div class="row justify-content-center">
    <div class="col-md-6 text-center">
        <h1 class="mb-4">Welcome to <strong>reBill</strong></h1>

        <!-- Bexio <PERSON> -->
        <div class="mb-4">
            <p>
                <a href="/auth/bexio" class="btn btn-dark btn-lg align-middle" style="background-color: #0D2F3B !important; font-weight: 600;">
                    Login with <img src="{{ asset('assets/bexio.png') }}" alt="bexio" style="max-width: 80px;border-radius: 5px; margin-right: -8px; margin-top: -10px;" />
                </a>
            </p>
        </div>

        <!-- Divider -->
        <div class="row">
            <div class="col">
                <hr class="my-4">
                <p class="text-muted">Or login with email and password</p>
            </div>
        </div>

        <!-- Manual Login Form -->
        <div class="card">
            <div class="card-body">
                <form method="POST" action="{{ route('login.manual') }}">
                    @csrf

                    @if ($errors->any())
                        <div class="alert alert-danger">
                            @foreach ($errors->all() as $error)
                                <div>{{ $error }}</div>
                            @endforeach
                        </div>
                    @endif

                    <div class="form-group text-left">
                        <label for="email">Email Address</label>
                        <input type="email" class="form-control @error('email') is-invalid @enderror"
                               id="email" name="email" value="{{ old('email') }}" required autofocus>
                        @error('email')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    <div class="form-group text-left">
                        <label for="password">Password</label>
                        <input type="password" class="form-control @error('password') is-invalid @enderror"
                               id="password" name="password" required>
                        @error('password')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    <div class="form-group text-left">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" name="remember" id="remember" {{ old('remember') ? 'checked' : '' }}>
                            <label class="form-check-label" for="remember">
                                Remember Me
                            </label>
                        </div>
                    </div>

                    <button type="submit" class="btn btn-primary btn-block">
                        Login
                    </button>
                </form>
            </div>
        </div>

        <div class="mt-3">
            <small class="text-muted">
                Admin users can login with email/password.<br>
                Regular users should use Bexio OAuth login.
            </small>
        </div>
    </div>
</div>
@endsection
