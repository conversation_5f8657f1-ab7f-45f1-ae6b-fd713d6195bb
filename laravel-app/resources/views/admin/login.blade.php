@extends('layouts.rebill')

@php
    $title = "<PERSON><PERSON> - <PERSON>";
@endphp

@section('content')
<div class="row justify-content-center">
    <div class="col-md-5">
        <div class="card shadow">
            <div class="card-header bg-primary text-white text-center">
                <h4 class="mb-0">
                    <i class="fas fa-shield-alt"></i> Admin <PERSON>gin
                </h4>
                <small><PERSON></small>
            </div>
            <div class="card-body">
                @if ($errors->any())
                    <div class="alert alert-danger">
                        @foreach ($errors->all() as $error)
                            <div>{{ $error }}</div>
                        @endforeach
                    </div>
                @endif

                <form method="POST" action="{{ route('admin.login.submit') }}">
                    @csrf

                    <div class="form-group">
                        <label for="email">
                            <i class="fas fa-envelope"></i> Email Address
                        </label>
                        <input type="email" class="form-control @error('email') is-invalid @enderror"
                               id="email" name="email" value="{{ old('email') }}"
                               placeholder="<EMAIL>" required autofocus>
                        @error('email')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    <div class="form-group">
                        <label for="password">
                            <i class="fas fa-lock"></i> Password
                        </label>
                        <input type="password" class="form-control @error('password') is-invalid @enderror"
                               id="password" name="password" placeholder="Enter your password" required>
                        @error('password')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    <div class="form-group">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" name="remember" id="remember"
                                   {{ old('remember') ? 'checked' : '' }}>
                            <label class="form-check-label" for="remember">
                                Remember Me
                            </label>
                        </div>
                    </div>

                    <button type="submit" class="btn btn-primary btn-block">
                        <i class="fas fa-sign-in-alt"></i> Login to Admin Panel
                    </button>
                </form>
            </div>
            <div class="card-footer text-center">
                <small class="text-muted">
                    <a href="{{ route('login') }}" class="text-primary">
                        <i class="fas fa-arrow-left"></i> Back to User Login
                    </a>
                </small>
            </div>
        </div>

        <!-- Info Card -->
        <div class="card mt-4 border-info">
            <div class="card-body text-center">
                <h6 class="text-info">
                    <i class="fas fa-info-circle"></i> Admin Access Only
                </h6>
                <small class="text-muted">
                    This login is for Kim Rebill administrators only.<br>
                    Regular users should use <a href="{{ route('login') }}">Bexio OAuth login</a>.
                </small>
            </div>
        </div>

        <!-- Test Credentials (only in development) -->
        @if(app()->environment('local'))
        <div class="card mt-3 border-warning">
            <div class="card-body">
                <h6 class="text-warning">
                    <i class="fas fa-code"></i> Development Credentials
                </h6>
                <small class="text-muted">
                    <strong>Email:</strong> <EMAIL><br>
                    <strong>Password:</strong> admin@2025
                </small>
            </div>
        </div>
        @endif
    </div>
</div>
@endsection
