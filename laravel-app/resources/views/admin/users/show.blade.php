@extends('layouts.app')

@section('title', 'User Details - ' . $user->name)

@section('content')
<div class="container-fluid">
    <!-- Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0 text-gray-800">User Details</h1>
            <p class="mb-0 text-muted">{{ $user->name }} ({{ $user->email }})</p>
        </div>
        <div>
            <a href="{{ route('admin.users.index') }}" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> Back to Users
            </a>
        </div>
    </div>

    <div class="row">
        <!-- User Information -->
        <div class="col-lg-8">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">User Information</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <table class="table table-borderless">
                                <tr>
                                    <td><strong>Name:</strong></td>
                                    <td>{{ $user->name }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Email:</strong></td>
                                    <td>{{ $user->email }}</td>
                                </tr>
                                <tr>
                                    <td><strong>User Type:</strong></td>
                                    <td>
                                        @if($user->is_admin)
                                            <span class="badge badge-info">Admin</span>
                                        @else
                                            <span class="badge badge-secondary">Regular User</span>
                                        @endif
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>Status:</strong></td>
                                    <td>
                                        @if($user->is_active)
                                            <span class="badge badge-success">Active</span>
                                        @else
                                            <span class="badge badge-danger">Suspended</span>
                                        @endif
                                    </td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-6">
                            <table class="table table-borderless">
                                <tr>
                                    <td><strong>Organization:</strong></td>
                                    <td>{{ $user->organization->name ?? 'No Organization' }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Bexio Integration:</strong></td>
                                    <td>
                                        @if($user->bexio_id)
                                            <span class="badge badge-success">Connected</span>
                                            <small class="d-block text-muted">ID: {{ $user->bexio_id }}</small>
                                        @else
                                            <span class="badge badge-warning">Not Connected</span>
                                        @endif
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>Created:</strong></td>
                                    <td>{{ $user->created_at->format('M d, Y H:i') }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Last Login:</strong></td>
                                    <td>{{ $userStats['last_login'] ?? 'Never' }}</td>
                                </tr>
                            </table>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Subscription Information -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-warning">Subscription Information</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <table class="table table-borderless">
                                <tr>
                                    <td><strong>Status:</strong></td>
                                    <td>
                                        <span class="badge badge-{{ $user->subscription_status === 'trial' ? 'warning' : ($user->subscription_status === 'active' ? 'success' : 'secondary') }}">
                                            {{ ucfirst($user->subscription_status) }}
                                        </span>
                                    </td>
                                </tr>
                                @if($user->trial_ends_at)
                                <tr>
                                    <td><strong>Trial Ends:</strong></td>
                                    <td>
                                        {{ $user->trial_ends_at->format('M d, Y') }}
                                        @if($user->isTrialExpired())
                                            <span class="badge badge-danger">Expired</span>
                                        @else
                                            <small class="text-muted">({{ $userStats['trial_remaining'] }} days left)</small>
                                        @endif
                                    </td>
                                </tr>
                                @endif
                            </table>
                        </div>
                        <div class="col-md-6">
                            <!-- Trial Management Actions -->
                            @if($user->subscription_status === 'trial')
                            <div class="mb-3">
                                <h6>Trial Management:</h6>
                                <form method="POST" action="{{ route('admin.users.update-status', $user) }}" class="d-inline">
                                    @csrf
                                    @method('PATCH')
                                    <input type="hidden" name="action" value="extend_trial">
                                    <div class="input-group input-group-sm" style="max-width: 200px;">
                                        <input type="number" name="days" class="form-control" placeholder="Days" min="1" max="365" value="30">
                                        <div class="input-group-append">
                                            <button type="submit" class="btn btn-outline-warning">Extend Trial</button>
                                        </div>
                                    </div>
                                </form>
                            </div>
                            @endif
                        </div>
                    </div>
                </div>
            </div>

            <!-- Recent Invoices -->
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-success">Recent Invoices</h6>
                </div>
                <div class="card-body">
                    @if($user->invoices->count() > 0)
                    <div class="table-responsive">
                        <table class="table table-bordered">
                            <thead>
                                <tr>
                                    <th>Title</th>
                                    <th>Document Nr</th>
                                    <th>Total</th>
                                    <th>Status</th>
                                    <th>Type</th>
                                    <th>Created</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($user->invoices as $invoice)
                                <tr>
                                    <td>{{ $invoice->title }}</td>
                                    <td>{{ $invoice->document_nr }}</td>
                                    <td>${{ number_format($invoice->total, 2) }}</td>
                                    <td>
                                        <span class="badge badge-{{ $invoice->status === 'active' ? 'success' : 'secondary' }}">
                                            {{ ucfirst($invoice->status) }}
                                        </span>
                                    </td>
                                    <td>
                                        @if($invoice->is_recurring)
                                            @if($invoice->created_from_recurring)
                                                <span class="badge badge-info">Generated</span>
                                            @else
                                                <span class="badge badge-primary">Template</span>
                                            @endif
                                        @else
                                            <span class="badge badge-secondary">One-time</span>
                                        @endif
                                    </td>
                                    <td>{{ $invoice->created_at->format('M d, Y') }}</td>
                                </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                    @else
                    <p class="text-muted text-center">No invoices found</p>
                    @endif
                </div>
            </div>
        </div>

        <!-- Actions & Stats -->
        <div class="col-lg-4">
            <!-- Quick Actions -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-danger">Quick Actions</h6>
                </div>
                <div class="card-body">
                    @if($user->is_active)
                        <button type="button" class="btn btn-warning btn-block mb-2" 
                                onclick="updateUserStatus({{ $user->id }}, 'suspend')">
                            <i class="fas fa-pause"></i> Suspend User
                        </button>
                    @else
                        <button type="button" class="btn btn-success btn-block mb-2" 
                                onclick="updateUserStatus({{ $user->id }}, 'activate')">
                            <i class="fas fa-play"></i> Activate User
                        </button>
                    @endif

                    @if($user->subscription_status === 'trial')
                        <button type="button" class="btn btn-primary btn-block mb-2" 
                                onclick="updateUserStatus({{ $user->id }}, 'activate_subscription')">
                            <i class="fas fa-crown"></i> Activate Subscription
                        </button>
                    @endif

                    @if(!$user->is_admin || \App\Models\User::admins()->count() > 1)
                        <button type="button" class="btn btn-danger btn-block" 
                                onclick="deleteUser({{ $user->id }}, '{{ $user->name }}')">
                            <i class="fas fa-trash"></i> Delete User
                        </button>
                    @endif
                </div>
            </div>

            <!-- User Statistics -->
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-info">User Statistics</h6>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-6">
                            <div class="border-right">
                                <h4 class="text-primary">{{ $userStats['total_invoices'] }}</h4>
                                <small class="text-muted">Total Invoices</small>
                            </div>
                        </div>
                        <div class="col-6">
                            <h4 class="text-success">{{ $userStats['recurring_templates'] }}</h4>
                            <small class="text-muted">Recurring Templates</small>
                        </div>
                    </div>
                    <hr>
                    <div class="row text-center">
                        <div class="col-12">
                            <h4 class="text-info">{{ $userStats['generated_invoices'] }}</h4>
                            <small class="text-muted">Generated from Recurring</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Status Update Form -->
<form id="statusForm" method="POST" style="display: none;">
    @csrf
    @method('PATCH')
    <input type="hidden" name="action" id="statusAction">
</form>

<!-- Delete Form -->
<form id="deleteForm" method="POST" style="display: none;">
    @csrf
    @method('DELETE')
</form>

<script>
function updateUserStatus(userId, action) {
    let message = `Are you sure you want to ${action.replace('_', ' ')} this user?`;
    
    if (confirm(message)) {
        const form = document.getElementById('statusForm');
        form.action = `/admin/users/${userId}/status`;
        document.getElementById('statusAction').value = action;
        form.submit();
    }
}

function deleteUser(userId, userName) {
    if (confirm(`Are you sure you want to delete user "${userName}"? This action cannot be undone.`)) {
        const form = document.getElementById('deleteForm');
        form.action = `/admin/users/${userId}`;
        form.submit();
    }
}
</script>
@endsection
