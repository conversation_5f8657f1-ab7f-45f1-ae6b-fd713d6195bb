@extends('layouts.app')

@section('title', 'Create New User')

@section('content')
<div class="container-fluid">
    <!-- Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0 text-gray-800">Create New Admin User</h1>
            <p class="mb-0 text-muted">Add a new admin user to Kim Rebill system</p>
        </div>
        <div>
            <a href="{{ route('admin.users.index') }}" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> Back to Users
            </a>
        </div>
    </div>

    <div class="row">
        <div class="col-lg-8">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Admin User Information</h6>
                </div>
                <div class="card-body">
                    <form method="POST" action="{{ route('admin.users.store') }}">
                        @csrf

                        <!-- Basic Information -->
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="name" class="form-label">Full Name <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control @error('name') is-invalid @enderror"
                                           id="name" name="name" value="{{ old('name') }}" required>
                                    @error('name')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="email" class="form-label">Email Address <span class="text-danger">*</span></label>
                                    <input type="email" class="form-control @error('email') is-invalid @enderror"
                                           id="email" name="email" value="{{ old('email') }}" required>
                                    @error('email')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <!-- Password -->
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="password" class="form-label">Password <span class="text-danger">*</span></label>
                                    <input type="password" class="form-control @error('password') is-invalid @enderror"
                                           id="password" name="password" required>
                                    @error('password')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                    <small class="form-text text-muted">Minimum 8 characters</small>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="password_confirmation" class="form-label">Confirm Password <span class="text-danger">*</span></label>
                                    <input type="password" class="form-control"
                                           id="password_confirmation" name="password_confirmation" required>
                                </div>
                            </div>
                        </div>

                        <!-- Organization -->
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="organization_id" class="form-label">Organization</label>
                                    <select name="organization_id" id="organization_id" class="form-control @error('organization_id') is-invalid @enderror">
                                        <option value="">Select Organization (Optional)</option>
                                        @foreach($organizations as $org)
                                            <option value="{{ $org->id }}" {{ old('organization_id') == $org->id ? 'selected' : '' }}>
                                                {{ $org->name }}
                                            </option>
                                        @endforeach
                                    </select>
                                    @error('organization_id')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="form-label">User Type</label>
                                    <div class="alert alert-info">
                                        <i class="fas fa-shield-alt"></i> <strong>Admin User</strong><br>
                                        <small>This form creates admin users only. Regular users are created via Bexio OAuth.</small>
                                    </div>
                                    <input type="hidden" name="is_admin" value="1">
                                </div>
                            </div>
                        </div>

                        <!-- Admin Settings -->
                        <div class="card mt-4">
                            <div class="card-header">
                                <h6 class="m-0 font-weight-bold text-secondary">Admin Settings</h6>
                            </div>
                            <div class="card-body">
                                <div class="alert alert-warning">
                                    <i class="fas fa-info-circle"></i> <strong>Admin users have full system access regardless of subscription status.</strong>
                                </div>
                                <input type="hidden" name="subscription_status" value="active">
                                <input type="hidden" name="trial_days" value="">
                            </div>
                        </div>

                        <!-- Submit Buttons -->
                        <div class="form-group mt-4">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-shield-alt"></i> Create Admin User
                            </button>
                            <a href="{{ route('admin.users.index') }}" class="btn btn-secondary ml-2">Cancel</a>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- Help Card -->
        <div class="col-lg-4">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-info">User Creation Guide</h6>
                </div>
                <div class="card-body">
                    <h6 class="font-weight-bold">User Types:</h6>
                    <ul class="list-unstyled">
                        <li><strong>Regular User:</strong> Can create and manage invoices</li>
                        <li><strong>Admin User:</strong> Can manage all users and system settings</li>
                    </ul>

                    <h6 class="font-weight-bold mt-3">Subscription Status:</h6>
                    <ul class="list-unstyled">
                        <li><strong>Trial:</strong> Limited time access</li>
                        <li><strong>Active:</strong> Full access to all features</li>
                        <li><strong>Suspended:</strong> Account temporarily disabled</li>
                    </ul>

                    <h6 class="font-weight-bold mt-3">Important Notes:</h6>
                    <ul class="list-unstyled">
                        <li>• Users created manually don't have Bexio integration initially</li>
                        <li>• Bexio ID will be populated when user first logs in via Bexio OAuth</li>
                        <li>• Admin users can access admin panel regardless of subscription status</li>
                        <li>• Trial users will be automatically suspended when trial expires</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const subscriptionStatus = document.getElementById('subscription_status');
    const trialDaysContainer = document.getElementById('trialDaysContainer');

    function toggleTrialDays() {
        if (subscriptionStatus.value === 'trial') {
            trialDaysContainer.style.display = 'block';
        } else {
            trialDaysContainer.style.display = 'none';
        }
    }

    subscriptionStatus.addEventListener('change', toggleTrialDays);
    toggleTrialDays(); // Initial call
});
</script>
@endsection
