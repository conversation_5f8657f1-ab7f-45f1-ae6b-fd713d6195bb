@extends('layouts.app')

@section('title', 'User Management')

@section('content')
<div class="container-fluid">
    <!-- Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0 text-gray-800">Admin Management</h1>
            <p class="mb-0 text-muted"><PERSON><PERSON> Kim Rebill admin users</p>
        </div>
        <div>
            <a href="{{ route('admin.users.create') }}" class="btn btn-primary">
                <i class="fas fa-shield-alt"></i> Create New Admin
            </a>
        </div>
    </div>

    <!-- Filters -->
    <div class="card shadow mb-4">
        <div class="card-body">
            <form method="GET" action="{{ route('admin.users.index') }}" class="row">
                <div class="col-md-3">
                    <label for="status" class="form-label">Status Filter</label>
                    <select name="status" id="status" class="form-control">
                        <option value="">All Admins</option>
                        <option value="active" {{ request('status') === 'active' ? 'selected' : '' }}>Active</option>
                        <option value="suspended" {{ request('status') === 'suspended' ? 'selected' : '' }}>Suspended</option>
                    </select>
                </div>
                <div class="col-md-4">
                    <label for="search" class="form-label">Search</label>
                    <input type="text" name="search" id="search" class="form-control"
                           placeholder="Search by name or email..." value="{{ request('search') }}">
                </div>
                <div class="col-md-2 d-flex align-items-end">
                    <button type="submit" class="btn btn-outline-primary me-2">Filter</button>
                    <a href="{{ route('admin.users.index') }}" class="btn btn-outline-secondary">Clear</a>
                </div>
            </form>
        </div>
    </div>

    <!-- Users Table -->
    <div class="card shadow">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">Admin Users ({{ $users->total() }} total)</h6>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-bordered" id="usersTable">
                    <thead>
                        <tr>
                            <th>
                                <input type="checkbox" id="selectAll">
                            </th>
                            <th>Admin User</th>
                            <th>Email</th>
                            <th>Last Login</th>
                            <th>Created</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        @forelse($users as $user)
                        <tr>
                            <td>
                                <input type="checkbox" name="user_ids[]" value="{{ $user->id }}" class="user-checkbox">
                            </td>
                            <td>
                                <div class="d-flex align-items-center">
                                    <div>
                                        <div class="font-weight-bold">{{ $user->name }}</div>
                                        @if($user->is_admin)
                                            <span class="badge badge-info">Admin</span>
                                        @endif
                                        @if($user->bexio_id)
                                            <span class="badge badge-secondary">Bexio User</span>
                                        @endif
                                    </div>
                                </div>
                            </td>
                            <td>{{ $user->email }}</td>
                            <td>
                                {{ $user->last_login_at ? $user->last_login_at->diffForHumans() : 'Never' }}
                            </td>
                            <td>
                                {{ $user->created_at->format('M d, Y') }}
                            </td>
                            <td>
                                <div class="btn-group" role="group">
                                    <a href="{{ route('admin.users.show', $user) }}" class="btn btn-sm btn-outline-primary">View</a>

                                    @if($user->is_active)
                                        <button type="button" class="btn btn-sm btn-outline-warning"
                                                onclick="updateUserStatus({{ $user->id }}, 'suspend')">Suspend</button>
                                    @else
                                        <button type="button" class="btn btn-sm btn-outline-success"
                                                onclick="updateUserStatus({{ $user->id }}, 'activate')">Activate</button>
                                    @endif

                                    @if(!$user->is_admin || \App\Models\User::admins()->count() > 1)
                                        <button type="button" class="btn btn-sm btn-outline-danger"
                                                onclick="deleteUser({{ $user->id }}, '{{ $user->name }}')">Delete</button>
                                    @endif
                                </div>
                            </td>
                        </tr>
                        @empty
                        <tr>
                            <td colspan="7" class="text-center">No admin users found</td>
                        </tr>
                        @endforelse
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            <div class="d-flex justify-content-between align-items-center mt-3">
                <div>
                    Showing {{ $users->firstItem() ?? 0 }} to {{ $users->lastItem() ?? 0 }} of {{ $users->total() }} results
                </div>
                <div>
                    {{ $users->links() }}
                </div>
            </div>
        </div>
    </div>

    <!-- Bulk Actions -->
    <div class="card shadow mt-4" id="bulkActions" style="display: none;">
        <div class="card-body">
            <div class="d-flex align-items-center">
                <span class="me-3">Bulk Actions for <span id="selectedCount">0</span> users:</span>
                <button type="button" class="btn btn-success me-2" onclick="bulkAction('activate')">Activate</button>
                <button type="button" class="btn btn-warning me-2" onclick="bulkAction('suspend')">Suspend</button>
                <button type="button" class="btn btn-danger" onclick="bulkAction('delete')">Delete</button>
            </div>
        </div>
    </div>
</div>

<!-- Status Update Form -->
<form id="statusForm" method="POST" style="display: none;">
    @csrf
    @method('PATCH')
    <input type="hidden" name="action" id="statusAction">
</form>

<!-- Delete Form -->
<form id="deleteForm" method="POST" style="display: none;">
    @csrf
    @method('DELETE')
</form>

<!-- Bulk Action Form -->
<form id="bulkForm" method="POST" action="{{ route('admin.users.bulk-action') }}" style="display: none;">
    @csrf
    <input type="hidden" name="action" id="bulkAction">
    <div id="bulkUserIds"></div>
</form>

<script>
// User selection handling
document.addEventListener('DOMContentLoaded', function() {
    const selectAll = document.getElementById('selectAll');
    const userCheckboxes = document.querySelectorAll('.user-checkbox');
    const bulkActions = document.getElementById('bulkActions');
    const selectedCount = document.getElementById('selectedCount');

    selectAll.addEventListener('change', function() {
        userCheckboxes.forEach(checkbox => {
            checkbox.checked = this.checked;
        });
        updateBulkActions();
    });

    userCheckboxes.forEach(checkbox => {
        checkbox.addEventListener('change', updateBulkActions);
    });

    function updateBulkActions() {
        const selected = document.querySelectorAll('.user-checkbox:checked');
        if (selected.length > 0) {
            bulkActions.style.display = 'block';
            selectedCount.textContent = selected.length;
        } else {
            bulkActions.style.display = 'none';
        }
    }
});

function updateUserStatus(userId, action) {
    if (confirm(`Are you sure you want to ${action} this user?`)) {
        const form = document.getElementById('statusForm');
        form.action = `/admin/users/${userId}/status`;
        document.getElementById('statusAction').value = action;
        form.submit();
    }
}

function deleteUser(userId, userName) {
    if (confirm(`Are you sure you want to delete user "${userName}"? This action cannot be undone.`)) {
        const form = document.getElementById('deleteForm');
        form.action = `/admin/users/${userId}`;
        form.submit();
    }
}

function bulkAction(action) {
    const selected = document.querySelectorAll('.user-checkbox:checked');
    if (selected.length === 0) {
        alert('Please select users first');
        return;
    }

    if (confirm(`Are you sure you want to ${action} ${selected.length} users?`)) {
        const form = document.getElementById('bulkForm');
        const bulkUserIds = document.getElementById('bulkUserIds');

        // Clear previous inputs
        bulkUserIds.innerHTML = '';

        // Add selected user IDs
        selected.forEach(checkbox => {
            const input = document.createElement('input');
            input.type = 'hidden';
            input.name = 'user_ids[]';
            input.value = checkbox.value;
            bulkUserIds.appendChild(input);
        });

        document.getElementById('bulkAction').value = action;
        form.submit();
    }
}
</script>
@endsection
