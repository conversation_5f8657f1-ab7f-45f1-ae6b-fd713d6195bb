<?php

require_once __DIR__ . '/vendor/autoload.php';

use Illuminate\Foundation\Application;
use Illuminate\Http\Request;

// Bootstrap Laravel
$app = require_once __DIR__ . '/bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

echo "🧪 Testing Bexio Invoice Process\n";
echo "================================\n\n";

// Test data
$testInvoiceId = 1; // Assuming we have an invoice with ID 1
$testEmail = '<EMAIL>';

echo "📋 Test Configuration:\n";
echo "- Invoice ID: {$testInvoiceId}\n";
echo "- Recipient Email: {$testEmail}\n";
echo "- Bexio Mode: " . (config('bexio.use_personal_token') ? 'Personal Token' : 'OAuth') . "\n\n";

// Check if we have the required configuration
if (!config('bexio.personal_access_token')) {
    echo "❌ Error: BEXIO_PERSONAL_ACCESS_TOKEN not configured\n";
    echo "Please add your Bexio Personal Access Token to .env file\n";
    exit(1);
}

// Check if invoice exists
try {
    $invoice = \App\Models\Invoice::find($testInvoiceId);
    if (!$invoice) {
        echo "❌ Error: Invoice with ID {$testInvoiceId} not found\n";
        echo "Please create an invoice first or change the test invoice ID\n";
        exit(1);
    }

    echo "✅ Invoice found: {$invoice->title}\n";
    echo "   - Document Nr: {$invoice->document_nr}\n";
    echo "   - Total: CHF " . number_format($invoice->total, 2) . "\n";
    echo "   - Status: {$invoice->status}\n";
    echo "   - Bexio ID: " . ($invoice->bexio_id ? $invoice->bexio_id : 'Not created yet') . "\n\n";

} catch (Exception $e) {
    echo "❌ Error checking invoice: " . $e->getMessage() . "\n";
    exit(1);
}

// Test 1: Create Invoice in Bexio (if not already created)
echo "🔄 Step 1: Create Invoice in Bexio\n";
echo "==================================\n";

if ($invoice->bexio_id) {
    echo "ℹ️  Invoice already exists in Bexio (ID: {$invoice->bexio_id})\n";
    echo "   Skipping creation step...\n\n";
} else {
    try {
        // Simulate authentication by setting user
        $user = \App\Models\User::find($invoice->user_id);
        \Illuminate\Support\Facades\Auth::login($user);

        // Create controller instance and call method directly
        $controller = new \App\Http\Controllers\Api\BexioApiController();
        $request = new \Illuminate\Http\Request();
        $request->merge(['invoice_id' => $testInvoiceId]);

        echo "📡 Calling Bexio API to create invoice...\n";
        $response = $controller->createInvoice($request);
        $result = json_decode($response->getContent(), true);

        if (isset($result['success']) && $result['success']) {
            echo "✅ Invoice created successfully in Bexio!\n";
            echo "   - Bexio ID: {$result['bexio_id']}\n";
            echo "   - Message: {$result['message']}\n\n";

            // Refresh invoice data
            $invoice->refresh();
        } else {
            echo "❌ Failed to create invoice in Bexio\n";
            echo "   - Error: " . ($result['error'] ?? 'Unknown error') . "\n\n";
        }

    } catch (Exception $e) {
        echo "❌ Error creating invoice: " . $e->getMessage() . "\n\n";
    }
}

// Test 2: Send Invoice Email with Template
echo "📧 Step 2: Send Invoice Email with Template\n";
echo "==========================================\n";

if (!$invoice->bexio_id) {
    echo "❌ Cannot send email: Invoice not created in Bexio yet\n";
    exit(1);
}

try {
    // Create controller instance and call method directly
    $controller = new \App\Http\Controllers\Api\BexioApiController();
    $request = new \Illuminate\Http\Request();
    $request->merge([
        'invoice_id' => $testInvoiceId,
        'recipient_email' => $testEmail
    ]);

    echo "📧 Calling Bexio API to send invoice email...\n";
    $response = $controller->sendInvoiceEmail($request);
    $result = json_decode($response->getContent(), true);

    if (isset($result['success']) && $result['success']) {
        echo "✅ Invoice email sent successfully!\n";
        echo "   - Recipient: {$testEmail}\n";
        echo "   - Message: {$result['message']}\n\n";
    } else {
        echo "❌ Failed to send invoice email\n";
        echo "   - Error: " . ($result['error'] ?? 'Unknown error') . "\n\n";
    }

} catch (Exception $e) {
    echo "❌ Error sending email: " . $e->getMessage() . "\n\n";
}

// Test 3: Show Template Preview
echo "📄 Step 3: Template Preview\n";
echo "===========================\n";

try {
    // Get user (assuming user ID 1 for testing)
    $user = \App\Models\User::find(1);
    if (!$user) {
        echo "❌ Test user not found\n";
    } else {
        $template = \App\Helpers\EmailTemplateHelper::getUserTemplate($user);

        // Prepare template variables
        $templateData = [
            'total' => number_format($invoice->total, 2),
            'date' => now()->format('M j, Y'),
            'valid_until' => now()->addDays(30)->format('M j, Y'),
            'document_number' => $invoice->document_nr,
            'title' => $invoice->title,
            'currency' => 'CHF',
            'name' => $invoice->contact_info['name'] ?? 'Customer',
            'name_1' => $invoice->contact_info['name'] ?? 'Customer',
            'user' => $user->name,
            'user_email' => $user->email,
            'company_name' => 'Your Company',
            'network_link' => "https://office.bexio.com/index.php/kb_invoice/show/id/{$invoice->bexio_id}"
        ];

        // Replace template variables
        $subject = \App\Helpers\EmailTemplateHelper::replaceVariables($template['subject'], $templateData);
        $body = \App\Helpers\EmailTemplateHelper::replaceVariables($template['body'], $templateData);

        echo "📧 Email Subject: {$subject}\n\n";
        echo "📝 Email Body:\n";
        echo str_repeat('-', 50) . "\n";
        echo $body . "\n";
        echo str_repeat('-', 50) . "\n\n";
    }

} catch (Exception $e) {
    echo "❌ Error generating template preview: " . $e->getMessage() . "\n\n";
}

echo "🎉 Test completed!\n";
echo "\n📚 API Endpoints Available:\n";
echo "- POST /api/bexio/create-invoice\n";
echo "- POST /api/bexio/send-invoice-email\n";
echo "\n💡 Next Steps:\n";
echo "1. Test with real Bexio account\n";
echo "2. Add proper authentication\n";
echo "3. Add error handling and retry logic\n";
echo "4. Implement cron job for automatic processing\n";
