<?php

/**
 * Test Create Customer API Endpoint
 * 
 * This script tests the createCustomer function in BexioApiController
 * using the corrected Personal Access Token approach
 */

require_once __DIR__ . '/vendor/autoload.php';

use Illuminate\Support\Facades\Http;

// Configuration
$baseAppUrl = 'http://127.0.0.1:8000'; // Laravel app URL
$endpoint = '/api/bexio/create-customer';

/**
 * Test data for creating customer
 */
$testCustomers = [
    [
        'name' => 'Kim Rebill Test Customer 1',
        'email' => '<EMAIL>',
        'type' => 1, // Customer
        'address' => 'Test Street 123',
        'street_name' => 'Test Street',
        'house_number' => '123',
        'city' => 'Zurich',
        'postcode' => '8001',
        'phone' => '+41 44 123 45 67',
        'mobile' => '+41 79 123 45 67',
        'country_id' => 1, // Switzerland
        'remarks' => 'Test customer created by Kim Rebill API test'
    ],
    [
        'name' => 'Kim Rebill Test Company 2',
        'email' => '<EMAIL>',
        'type' => 1, // Customer
        'address' => 'Business Avenue 456',
        'street_name' => 'Business Avenue',
        'house_number' => '456',
        'city' => 'Basel',
        'postcode' => '4001',
        'phone' => '+41 61 987 65 43',
        'country_id' => 1,
        'website' => 'https://kimrebill.com',
        'remarks' => 'Second test customer for API validation'
    ]
];

/**
 * Test create customer endpoint
 */
function testCreateCustomer($customerData, $testNumber) {
    global $baseAppUrl, $endpoint;
    
    echo "\n" . str_repeat("=", 60) . "\n";
    echo "Test #{$testNumber}: Creating Customer - {$customerData['name']}\n";
    echo str_repeat("=", 60) . "\n";
    
    echo "Customer Data:\n";
    echo "- Name: {$customerData['name']}\n";
    echo "- Email: {$customerData['email']}\n";
    echo "- Address: {$customerData['address']}, {$customerData['city']}\n";
    echo "- Phone: {$customerData['phone']}\n";
    
    try {
        $response = Http::timeout(30)->post($baseAppUrl . $endpoint, [
            'customer' => $customerData
        ]);
        
        if ($response->successful()) {
            $data = $response->json();
            echo "\n✓ SUCCESS - Status: {$response->status()}\n";
            
            if (isset($data['success']) && $data['success']) {
                echo "✓ Customer created successfully!\n";
                echo "✓ Bexio ID: {$data['data']['id']}\n";
                echo "✓ Name: {$data['data']['name_1']}\n";
                echo "✓ Email: {$data['data']['mail']}\n";
                echo "✓ Address: {$data['data']['address']}, {$data['data']['city']}\n";
                
                return [
                    'success' => true,
                    'bexio_id' => $data['data']['id'],
                    'data' => $data['data']
                ];
            } else {
                echo "✗ API returned success=false\n";
                echo "✗ Message: " . ($data['message'] ?? 'Unknown error') . "\n";
                return ['success' => false, 'error' => $data['message'] ?? 'Unknown error'];
            }
        } else {
            $errorData = $response->json();
            echo "✗ FAILED - Status: {$response->status()}\n";
            echo "✗ Error: " . ($errorData['error'] ?? 'Unknown error') . "\n";
            echo "✗ Message: " . ($errorData['message'] ?? 'No message') . "\n";
            
            if (isset($errorData['status_code'])) {
                echo "✗ Bexio Status: {$errorData['status_code']}\n";
            }
            
            return ['success' => false, 'error' => $errorData['message'] ?? 'HTTP error'];
        }
        
    } catch (Exception $e) {
        echo "✗ EXCEPTION: {$e->getMessage()}\n";
        return ['success' => false, 'error' => $e->getMessage()];
    }
}

/**
 * Test endpoint availability
 */
function testEndpointAvailability() {
    global $baseAppUrl;
    
    echo "Testing Laravel App Availability\n";
    echo "================================\n";
    
    try {
        $response = Http::timeout(10)->get($baseAppUrl);
        
        if ($response->successful()) {
            echo "✓ Laravel app is running at {$baseAppUrl}\n";
            return true;
        } else {
            echo "✗ Laravel app returned status: {$response->status()}\n";
            return false;
        }
    } catch (Exception $e) {
        echo "✗ Cannot connect to Laravel app: {$e->getMessage()}\n";
        echo "✗ Make sure to run: php artisan serve\n";
        return false;
    }
}

/**
 * Check environment configuration
 */
function checkEnvironment() {
    echo "\nChecking Environment Configuration\n";
    echo "=================================\n";
    
    // Check if .env file exists
    if (!file_exists(__DIR__ . '/.env')) {
        echo "✗ .env file not found\n";
        return false;
    }
    
    // Check for Personal Access Token
    $envContent = file_get_contents(__DIR__ . '/.env');
    if (strpos($envContent, 'BEXIO_PERSONAL_ACCESS_TOKEN') === false) {
        echo "✗ BEXIO_PERSONAL_ACCESS_TOKEN not found in .env\n";
        return false;
    }
    
    echo "✓ .env file exists\n";
    echo "✓ BEXIO_PERSONAL_ACCESS_TOKEN configured\n";
    
    return true;
}

// Main execution
echo "Kim Rebill - Create Customer API Test\n";
echo "====================================\n";

// Check environment
if (!checkEnvironment()) {
    echo "\n❌ Environment check failed. Please fix configuration.\n";
    exit(1);
}

// Check Laravel app availability
if (!testEndpointAvailability()) {
    echo "\n❌ Laravel app is not accessible. Please start the server.\n";
    exit(1);
}

// Run tests
$results = [];
foreach ($testCustomers as $index => $customerData) {
    $result = testCreateCustomer($customerData, $index + 1);
    $results[] = $result;
    
    // Wait a bit between requests
    sleep(1);
}

// Summary
echo "\n" . str_repeat("=", 60) . "\n";
echo "TEST SUMMARY\n";
echo str_repeat("=", 60) . "\n";

$successCount = 0;
$failCount = 0;

foreach ($results as $index => $result) {
    $testNum = $index + 1;
    if ($result['success']) {
        echo "Test #{$testNum}: ✓ PASSED";
        if (isset($result['bexio_id'])) {
            echo " (Bexio ID: {$result['bexio_id']})";
        }
        echo "\n";
        $successCount++;
    } else {
        echo "Test #{$testNum}: ✗ FAILED - {$result['error']}\n";
        $failCount++;
    }
}

echo "\nResults: {$successCount} passed, {$failCount} failed\n";

if ($successCount > 0) {
    echo "\n🎉 Customer creation is working with Personal Access Token!\n";
} else {
    echo "\n❌ All tests failed. Please check:\n";
    echo "1. BEXIO_PERSONAL_ACCESS_TOKEN in .env\n";
    echo "2. Laravel server running (php artisan serve)\n";
    echo "3. Internet connection\n";
    echo "4. Bexio API permissions\n";
}

echo "\nDone!\n";
