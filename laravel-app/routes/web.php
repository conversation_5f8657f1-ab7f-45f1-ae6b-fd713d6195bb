<?php

use Illuminate\Support\Facades\Route;
use Illuminate\Support\Facades\Auth;
use App\Http\Controllers\Auth\BexioAuthController;
use App\Http\Controllers\DashboardController;
use App\Http\Controllers\InvoiceController;
use App\Http\Controllers\SettingsController;
use App\Http\Controllers\SubscriptionController;
use App\Http\Controllers\Api\BexioApiController;
use App\Http\Controllers\Admin\UserManagementController;
/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| contains the "web" middleware group. Now create something great!
|
*/

Route::get('/', function () {
    return Auth::check() ? redirect('/dashboard') : redirect('/login');
});

Route::get('/login', [BexioAuthController::class, 'showLoginForm'])
    ->name('login')
    ->middleware('guest');

// Admin login routes
Route::get('/admin/login', [BexioAuthController::class, 'showAdminLoginForm'])
    ->name('admin.login')
    ->middleware('guest');

Route::post('/admin/login', [BexioAuthController::class, 'adminLogin'])
    ->name('admin.login.submit')
    ->middleware('guest');

Route::get('/auth/bexio', [BexioAuthController::class, 'redirect'])
    ->name('bexio.login')
    ->middleware('guest');

Route::get('/auth/callback', [BexioAuthController::class, 'callback'])
    ->name('bexio.auth.callback');

Route::post('/logout', [BexioAuthController::class, 'logout'])
    ->name('logout')
    ->middleware('auth');

Route::get('/home', function () {
    return redirect('/dashboard');
});

Route::get('/dashboard', [DashboardController::class, 'index'])
    ->middleware(['auth', 'fresh-token'])
    ->name('dashboard');

// Subscription routes (accessible even with inactive subscription)
Route::middleware('auth')->group(function () {
    Route::post('/subscription/request-activation', [SubscriptionController::class, 'requestActivation'])
        ->name('subscription.request-activation');
});

// Invoice routes
Route::middleware(['auth', 'fresh-token'])->group(function () {
    Route::get('/invoices', [InvoiceController::class, 'index'])->name('invoices.index');
    Route::get('/invoices/create', [InvoiceController::class, 'create'])->name('invoices.create');
    Route::post('/invoices', [InvoiceController::class, 'store'])->name('invoices.store');
    Route::get('/invoices/{invoice}', [InvoiceController::class, 'show'])->name('invoices.show');
    Route::get('/invoices/{invoice}/edit', [InvoiceController::class, 'edit'])->name('invoices.edit');
    Route::put('/invoices/{invoice}', [InvoiceController::class, 'update'])->name('invoices.update');
    Route::delete('/invoices/{invoice}', [InvoiceController::class, 'destroy'])->name('invoices.destroy');

    // Drafts route
    Route::get('/drafts', [InvoiceController::class, 'drafts'])->name('drafts.index');
});

// Bexio API routes for authenticated users
Route::middleware(['auth'])->prefix('api/bexio')->group(function () {
    Route::get('/customers', [BexioApiController::class, 'getCustomers']);
    Route::get('/units', [BexioApiController::class, 'getUnits']);
    Route::get('/taxes', [BexioApiController::class, 'getTaxes']);
    Route::post('/create-customer', [BexioApiController::class, 'createCustomer']);
});

// Test routes for development (no auth required)
if (app()->environment('local')) {
    Route::prefix('api/bexio-test')->group(function () {
        Route::get('/customers', [BexioApiController::class, 'getCustomers']);
        Route::get('/units', [BexioApiController::class, 'getUnits']);
        Route::get('/taxes', [BexioApiController::class, 'getTaxes']);
    });
}

// Settings routes
Route::middleware(['auth', 'fresh-token'])->group(function () {
    Route::get('/settings', [SettingsController::class, 'index'])->name('settings.index');
    Route::put('/settings/profile', [SettingsController::class, 'updateProfile'])->name('settings.profile.update');
    Route::put('/settings/templates', [SettingsController::class, 'updateTemplates'])->name('settings.templates.update');
    Route::put('/settings/password', [SettingsController::class, 'updatePassword'])->name('settings.password.update');
    Route::put('/settings/bexio', [SettingsController::class, 'updateBexioSettings'])->name('settings.bexio.update');
    Route::post('/settings/bexio/reset', [SettingsController::class, 'resetBexioConnection'])->name('settings.bexio.reset');
});

// Admin routes - only accessible by admin users
Route::middleware(['auth', 'admin'])->prefix('admin')->name('admin.')->group(function () {
    // Admin Dashboard
    Route::get('/', [UserManagementController::class, 'index'])->name('dashboard');

    // User Management
    Route::get('/users', [UserManagementController::class, 'users'])->name('users.index');
    Route::get('/users/create', [UserManagementController::class, 'create'])->name('users.create');
    Route::post('/users', [UserManagementController::class, 'store'])->name('users.store');
    Route::get('/users/{user}', [UserManagementController::class, 'show'])->name('users.show');
    Route::patch('/users/{user}/status', [UserManagementController::class, 'updateStatus'])->name('users.update-status');
    Route::delete('/users/{user}', [UserManagementController::class, 'destroy'])->name('users.destroy');
    Route::post('/users/bulk-action', [UserManagementController::class, 'bulkAction'])->name('users.bulk-action');
});
