<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Api\BexioApiController;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| is assigned the "api" middleware group. Enjoy building your API!
|
*/

Route::middleware('auth:sanctum')->get('/user', function (Request $request) {
    return $request->user();
});

// Bexio API routes - require authentication
Route::middleware(['auth'])->prefix('bexio')->group(function () {
    Route::get('/customers', [BexioApiController::class, 'getCustomers']);
    Route::get('/units', [BexioApiController::class, 'getUnits']);
    Route::get('/taxes', [BexioApiController::class, 'getTaxes']);
    Route::post('/create-customer', [BexioApiController::class, 'createCustomer']);
    Route::post('/create-invoice', [BexioApiController::class, 'createInvoice']);
    Route::post('/send-invoice-email', [BexioApiController::class, 'sendInvoiceEmail']);
});

// Test routes without auth (for development)
Route::prefix('bexio-test')->group(function () {
    Route::get('/customers', [BexioApiController::class, 'getCustomers']);
    Route::get('/units', [BexioApiController::class, 'getUnits']);
    Route::get('/taxes', [BexioApiController::class, 'getTaxes']);
});
