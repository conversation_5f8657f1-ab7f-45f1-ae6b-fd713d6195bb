# Bexio API Testing Suite

Testing suite untuk Bexio Contact API berdasarkan curl commands yang di<PERSON>ikan.

## 📋 Files yang Dibuat

1. **`test_bexio_simple.php`** - Testing sederhana menggunakan cURL
2. **`test_bexio_contact.php`** - Testing lengkap menggunakan Guzzle HTTP
3. **`test_bexio_laravel.php`** - Testing terintegrasi dengan Laravel
4. **`run_bexio_tests.sh`** - Script untuk menjalankan semua testing
5. **`BEXIO_TESTING.md`** - Dokumentasi ini

## 🚀 Cara Penggunaan

### 1. Setup Access Token

Tambahkan Bexio access token ke file `.env`:

```env
BEXIO_ACCESS_TOKEN=your_actual_bexio_token_here
```

### 2. Jalankan Testing

#### Opsi A: Menggunakan Script Otomatis
```bash
./run_bexio_tests.sh
```

#### Opsi B: Jalankan Manual

**Simple cURL Test:**
```bash
php test_bexio_simple.php
```

**Advanced Guzzle Test:**
```bash
php test_bexio_contact.php
```

**Lara<PERSON> Integration Test:**
```bash
php test_bexio_laravel.php
```

## 📝 API Endpoints yang Ditest

### GET /contact
```bash
curl -X GET \
  https://api.bexio.com/2.0/contact \
  -H 'Accept: application/json' \
  -H 'Authorization: Bearer {access-token}'
```

**Testing:**
- ✅ Retrieve semua contacts
- ✅ Menampilkan jumlah contacts
- ✅ Menampilkan detail contact pertama

### POST /contact
```bash
curl -X POST \
  https://api.bexio.com/2.0/contact \
  -H 'Content-Type: application/json' \
  -H 'Accept: application/json' \
  -H 'Authorization: Bearer {access-token}' \
  -d '{
    "contact_type_id": 1,
    "name_1": "Example Company",
    "salutation_id": 2,
    "address": "Smith Street 22",
    "street_name": "Smith Street",
    "house_number": "77",
    "address_addition": "Building C",
    "postcode": "8004",
    "city": "Zurich",
    "country_id": 1,
    "mail": "<EMAIL>",
    "mail_second": "",
    "phone_fixed": "",
    "phone_fixed_second": "",
    "phone_mobile": "",
    "fax": "",
    "url": "",
    "skype_name": "",
    "remarks": "",
    "contact_group_ids": "1,2",
    "user_id": 1,
    "owner_id": 1
  }'
```

**Testing:**
- ✅ Create new contact
- ✅ Menampilkan ID contact yang dibuat
- ✅ Verifikasi data yang tersimpan

## 🔧 Features Testing

### 1. Simple cURL Test (`test_bexio_simple.php`)
- ✅ Pure cURL implementation
- ✅ Minimal dependencies
- ✅ Manual token input
- ✅ Basic error handling

### 2. Advanced Guzzle Test (`test_bexio_contact.php`)
- ✅ Guzzle HTTP client
- ✅ Comprehensive error handling
- ✅ Auto token loading dari .env
- ✅ Detailed response analysis
- ✅ GET specific contact by ID

### 3. Laravel Integration Test (`test_bexio_laravel.php`)
- ✅ Laravel HTTP facade
- ✅ Environment integration
- ✅ Testing Laravel API endpoints
- ✅ BexioApiController testing

## 📊 Output Example

```
Bexio Contact API Testing
========================

✓ Access token configured
✓ Base URL: https://api.bexio.com/2.0

=== Testing GET /contact ===
✓ SUCCESS - HTTP 200
✓ Retrieved 15 contacts
✓ First contact: Example Company (ID: 123)

=== Testing POST /contact ===
Creating contact: Kim Rebill Test Company
✓ SUCCESS - HTTP 201
✓ Created contact with ID: 456
✓ Name: Kim Rebill Test Company
✓ Email: <EMAIL>

=== Test Summary ===
GET /contact: ✓ PASSED
POST /contact: ✓ PASSED
```

## 🛠️ Troubleshooting

### Error: "Access token not found"
```bash
# Pastikan .env file ada dan berisi:
BEXIO_ACCESS_TOKEN=your_token_here
```

### Error: "cURL error"
```bash
# Check internet connection dan firewall
curl -I https://api.bexio.com/2.0/contact
```

### Error: "Laravel server not running"
```bash
# Start Laravel development server
php artisan serve
```

### Error: "Permission denied"
```bash
# Make script executable
chmod +x run_bexio_tests.sh
```

## 🔗 Related Files

- **BexioApiController**: `app/Http/Controllers/BexioApiController.php`
- **API Routes**: `routes/api.php`
- **Environment**: `.env`
- **Laravel Config**: `config/services.php`

## 📚 Bexio API Documentation

- **Main Docs**: https://docs.bexio.com/
- **Contact API**: https://docs.bexio.com/#tag/Contacts
- **Authentication**: https://docs.bexio.com/#section/Authentication
- **Rate Limits**: https://docs.bexio.com/#section/Rate-Limiting

## ✅ Testing Checklist

- [ ] Access token configured in .env
- [ ] Laravel server running (php artisan serve)
- [ ] Internet connection available
- [ ] Bexio API accessible
- [ ] GET /contact returns data
- [ ] POST /contact creates contact
- [ ] Laravel API endpoints working
- [ ] Error handling working properly

## 🎯 Next Steps

1. **Integration Testing**: Test dengan real invoice creation
2. **Unit Testing**: Buat PHPUnit tests
3. **Error Scenarios**: Test dengan invalid data
4. **Performance Testing**: Test dengan large datasets
5. **Authentication Testing**: Test token refresh

Happy testing! 🚀
