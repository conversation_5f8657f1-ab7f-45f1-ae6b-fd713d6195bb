<?php

require_once __DIR__ . '/vendor/autoload.php';

use Illuminate\Foundation\Application;

// Bootstrap Laravel
$app = require_once __DIR__ . '/bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

echo "🔍 Testing Bexio Data Availability\n";
echo "==================================\n\n";

// Check configuration
if (!config('bexio.personal_access_token')) {
    echo "❌ Error: BEXIO_PERSONAL_ACCESS_TOKEN not configured\n";
    exit(1);
}

echo "✅ Bexio Personal Access Token configured\n\n";

// Test API endpoints
$controller = new \App\Http\Controllers\Api\BexioApiController();

// Test 1: Get Customers
echo "👥 Testing Customers API\n";
echo "========================\n";
try {
    $response = $controller->getCustomers();
    $customers = json_decode($response->getContent(), true);

    if (is_array($customers) && count($customers) > 0) {
        echo "✅ Found " . count($customers) . " customers\n";
        echo "📋 First few customers:\n";
        foreach (array_slice($customers, 0, 3) as $customer) {
            echo "   - ID: {$customer['id']}, Name: {$customer['name_1']}, Email: " . ($customer['mail'] ?? 'N/A') . "\n";
        }
        echo "\n";
    } else {
        echo "⚠️  No customers found or API error\n\n";
    }
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n\n";
}

// Test 2: Get Taxes
echo "💰 Testing Taxes API\n";
echo "===================\n";
try {
    $response = $controller->getTaxes();
    $taxes = json_decode($response->getContent(), true);

    if (is_array($taxes) && count($taxes) > 0) {
        echo "✅ Found " . count($taxes) . " taxes\n";
        echo "📋 Available taxes:\n";
        foreach (array_slice($taxes, 0, 5) as $tax) {
            $name = $tax['display_name'] ?? $tax['name'] ?? 'Unknown';
            $percentage = $tax['percentage'] ?? 'N/A';
            echo "   - ID: {$tax['id']}, Name: {$name}, Rate: {$percentage}%\n";
        }
        echo "\n";
    } else {
        echo "⚠️  No taxes found or API error\n\n";
    }
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n\n";
}

// Test 3: Get Units
echo "📏 Testing Units API\n";
echo "===================\n";
try {
    $response = $controller->getUnits();
    $units = json_decode($response->getContent(), true);

    if (is_array($units) && count($units) > 0) {
        echo "✅ Found " . count($units) . " units\n";
        echo "📋 Available units:\n";
        foreach (array_slice($units, 0, 5) as $unit) {
            echo "   - ID: {$unit['id']}, Name: {$unit['name']}\n";
        }
        echo "\n";
    } else {
        echo "⚠️  No units found or API error\n\n";
    }
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n\n";
}

echo "🎯 Recommendations for Invoice Creation:\n";
echo "========================================\n";

// Get first valid customer and tax
try {
    $customersResponse = $controller->getCustomers();
    $customers = json_decode($customersResponse->getContent(), true);

    $taxesResponse = $controller->getTaxes();
    $taxes = json_decode($taxesResponse->getContent(), true);

    $unitsResponse = $controller->getUnits();
    $units = json_decode($unitsResponse->getContent(), true);

    if (is_array($customers) && count($customers) > 0) {
        $firstCustomer = $customers[0];
        echo "✅ Use Customer ID: {$firstCustomer['id']} ({$firstCustomer['name_1']})\n";
    }

    if (is_array($taxes) && count($taxes) > 0) {
        $firstTax = $taxes[0];
        $taxName = isset($firstTax['display_name']) ? $firstTax['display_name'] : (isset($firstTax['name']) ? $firstTax['name'] : 'Unknown');
        echo "✅ Use Tax ID: {$firstTax['id']} ({$taxName})\n";
    }

    if (is_array($units) && count($units) > 0) {
        $firstUnit = $units[0];
        echo "✅ Use Unit ID: {$firstUnit['id']} ({$firstUnit['name']})\n";
    }

} catch (Exception $e) {
    echo "❌ Error getting recommendations: " . $e->getMessage() . "\n";
}

echo "\n💡 Next Steps:\n";
echo "1. Update test invoice with valid contact_id, tax_id, and unit_id\n";
echo "2. Re-run the invoice creation test\n";
echo "3. Test email sending functionality\n";
