#!/bin/bash

# Bexio API Testing Script
# This script runs various Bexio API tests

echo "======================================"
echo "Kim Rebill - Bexio API Testing Suite"
echo "======================================"

# Check if we're in the right directory
if [ ! -f "artisan" ]; then
    echo "❌ Error: Please run this script from the Laravel app directory"
    exit 1
fi

# Check if .env file exists
if [ ! -f ".env" ]; then
    echo "❌ Error: .env file not found"
    echo "Please create .env file with BEXIO_ACCESS_TOKEN"
    exit 1
fi

# Check if access token is set
if ! grep -q "BEXIO_ACCESS_TOKEN" .env; then
    echo "❌ Error: BEXIO_ACCESS_TOKEN not found in .env"
    echo "Please add: BEXIO_ACCESS_TOKEN=your_token_here"
    exit 1
fi

echo "✅ Environment checks passed"
echo ""

# Menu for test selection
echo "Select test to run:"
echo "1) Simple cURL test (requires manual token input)"
echo "2) Advanced test with Guzzle"
echo "3) Laravel integration test"
echo "4) All tests"
echo "5) Exit"
echo ""

read -p "Enter your choice (1-5): " choice

case $choice in
    1)
        echo ""
        echo "Running Simple cURL Test..."
        echo "================================"
        php test_bexio_simple.php
        ;;
    2)
        echo ""
        echo "Running Advanced Guzzle Test..."
        echo "================================"
        php test_bexio_contact.php
        ;;
    3)
        echo ""
        echo "Running Laravel Integration Test..."
        echo "=================================="
        php test_bexio_laravel.php
        ;;
    4)
        echo ""
        echo "Running All Tests..."
        echo "==================="
        
        echo ""
        echo "1/3 - Simple cURL Test:"
        echo "----------------------"
        php test_bexio_simple.php
        
        echo ""
        echo "2/3 - Advanced Guzzle Test:"
        echo "---------------------------"
        php test_bexio_contact.php
        
        echo ""
        echo "3/3 - Laravel Integration Test:"
        echo "-------------------------------"
        php test_bexio_laravel.php
        ;;
    5)
        echo "Exiting..."
        exit 0
        ;;
    *)
        echo "❌ Invalid choice. Please run the script again."
        exit 1
        ;;
esac

echo ""
echo "======================================"
echo "Testing completed!"
echo "======================================"

# Show useful information
echo ""
echo "📋 Useful Information:"
echo "• Bexio API Documentation: https://docs.bexio.com/"
echo "• Contact API: https://docs.bexio.com/#tag/Contacts"
echo "• Laravel App: http://127.0.0.1:8000"
echo "• API Routes: http://127.0.0.1:8000/api/bexio/"
echo ""

# Check if Laravel server is running
if curl -s http://127.0.0.1:8000 > /dev/null; then
    echo "✅ Laravel development server is running"
else
    echo "⚠️  Laravel development server is not running"
    echo "   Start it with: php artisan serve"
fi

echo ""
echo "Happy testing! 🚀"
