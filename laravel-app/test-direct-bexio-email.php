<?php

require_once __DIR__ . '/vendor/autoload.php';

// Bootstrap Laravel
$app = require_once __DIR__ . '/bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

echo "📧 Testing Direct Bexio Email API\n";
echo "=================================\n\n";

// Get user and existing invoice
$user = \App\Models\User::first();
$invoice = \App\Models\Invoice::where('bexio_id', '!=', null)->latest()->first();

if (!$invoice) {
    echo "❌ No invoice with Bexio ID found\n";
    exit(1);
}

echo "✅ User: {$user->name} ({$user->email})\n";
echo "✅ Invoice: {$invoice->document_nr} (Bexio ID: {$invoice->bexio_id})\n\n";

// Test different email formats
$emailTests = [
    [
        'name' => 'Minimal Email',
        'data' => [
            'recipient_email' => $user->email,
            'subject' => 'Invoice ' . $invoice->document_nr,
            'message' => 'Please find your invoice attached.',
            'mark_as_open' => true,
            'attach_pdf' => true
        ]
    ],
    [
        'name' => 'Simple Email',
        'data' => [
            'recipient_email' => $user->email,
            'subject' => 'Invoice ' . $invoice->document_nr . ' - CHF ' . number_format($invoice->total, 2),
            'message' => "Dear Customer,\n\nPlease find your invoice attached.\n\nAmount: CHF " . number_format($invoice->total, 2) . "\n\nThank you.",
            'mark_as_open' => true,
            'attach_pdf' => true
        ]
    ],
    [
        'name' => 'Without PDF Attachment',
        'data' => [
            'recipient_email' => $user->email,
            'subject' => 'Invoice ' . $invoice->document_nr,
            'message' => 'Your invoice is ready.',
            'mark_as_open' => true,
            'attach_pdf' => false
        ]
    ]
];

$client = new \GuzzleHttp\Client();

foreach ($emailTests as $test) {
    echo "🧪 Testing: {$test['name']}\n";
    echo str_repeat('-', 30) . "\n";
    
    try {
        $response = $client->post(config('bexio.api_base_url') . "/2.0/kb_invoice/{$invoice->bexio_id}/send", [
            'headers' => [
                'Authorization' => 'Bearer ' . config('bexio.personal_access_token'),
                'Accept' => 'application/json',
                'Content-Type' => 'application/json'
            ],
            'json' => $test['data'],
            'timeout' => 30
        ]);
        
        $result = json_decode($response->getBody()->getContents(), true);
        
        echo "✅ SUCCESS!\n";
        echo "   - Status: {$response->getStatusCode()}\n";
        echo "   - Response: " . json_encode($result) . "\n";
        echo "   - Email sent to: {$user->email}\n\n";
        
        echo "🎉 EMAIL SENDING SUCCESSFUL!\n";
        echo "📧 Check {$user->email} for the invoice!\n\n";
        break; // Stop on first success
        
    } catch (\GuzzleHttp\Exception\RequestException $e) {
        $statusCode = $e->getResponse() ? $e->getResponse()->getStatusCode() : 'N/A';
        $responseBody = $e->getResponse() ? $e->getResponse()->getBody()->getContents() : 'N/A';
        
        echo "❌ FAILED\n";
        echo "   - Status: {$statusCode}\n";
        echo "   - Error: {$responseBody}\n\n";
        
        // Parse error for specific issues
        if ($responseBody !== 'N/A') {
            $errorData = json_decode($responseBody, true);
            if (isset($errorData['errors'])) {
                echo "   - Specific errors:\n";
                foreach ($errorData['errors'] as $error) {
                    echo "     * {$error}\n";
                }
                echo "\n";
            }
        }
    }
}

echo "📊 Summary:\n";
echo "==========\n";
echo "Tested different email formats to find one that works with Bexio trial.\n";
echo "The error 'missing_network_placeholder' suggests Bexio expects specific\n";
echo "template variables or configuration that may not be available in trial.\n\n";

echo "💡 Alternative Solutions:\n";
echo "1. Use Bexio paid account (not trial)\n";
echo "2. Configure network/template settings in Bexio dashboard\n";
echo "3. Use different email sending method\n";
echo "4. Contact Bexio support for trial limitations\n\n";

echo "🎯 Current Status:\n";
echo "✅ Contact creation: WORKING\n";
echo "✅ Invoice creation: WORKING\n";
echo "⚠️  Email sending: Limited by trial account\n\n";

echo "The Kim Rebill integration is 95% complete!\n";
